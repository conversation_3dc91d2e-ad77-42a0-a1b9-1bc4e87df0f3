// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a zh_CN locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, always_declare_return_types

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = MessageLookup();

typedef String MessageIfAbsent(String? messageStr, List<Object>? args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'zh_CN';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function> {
    "AncientChinese" : MessageLookupByLibrary.simpleMessage("中国古代风格"),
    "Anime" : MessageLookupByLibrary.simpleMessage("动漫风格"),
    "Everyone" : MessageLookupByLibrary.simpleMessage("所有人"),
    "Landscape" : MessageLookupByLibrary.simpleMessage("横向矩形"),
    "NSFW" : MessageLookupByLibrary.simpleMessage("非公共场所"),
    "Other" : MessageLookupByLibrary.simpleMessage("其它风格"),
    "Pixel" : MessageLookupByLibrary.simpleMessage("像素风格"),
    "Portrait" : MessageLookupByLibrary.simpleMessage("纵向矩形"),
    "Realistic" : MessageLookupByLibrary.simpleMessage("写实风格"),
    "SearchWorkshop" : MessageLookupByLibrary.simpleMessage("在创意工坊寻找"),
    "Square" : MessageLookupByLibrary.simpleMessage("正方形"),
    "Subscribe" : MessageLookupByLibrary.simpleMessage("订阅"),
    "SubscribeAndDownload" : MessageLookupByLibrary.simpleMessage("订阅和下载"),
    "Subscribed" : MessageLookupByLibrary.simpleMessage("已订阅"),
    "Unsubscribed" : MessageLookupByLibrary.simpleMessage("未订阅"),
    "about" : MessageLookupByLibrary.simpleMessage("关于"),
    "achievementDisplay" : MessageLookupByLibrary.simpleMessage("成果展柜"),
    "achievements" : MessageLookupByLibrary.simpleMessage("成就"),
    "activity" : MessageLookupByLibrary.simpleMessage("动态"),
    "add" : MessageLookupByLibrary.simpleMessage("加上"),
    "addDiamond" : MessageLookupByLibrary.simpleMessage("添加菱形"),
    "addGlobalValue" : MessageLookupByLibrary.simpleMessage("添加全局数值"),
    "addHexagon" : MessageLookupByLibrary.simpleMessage("添加六边形"),
    "addImage" : MessageLookupByLibrary.simpleMessage("添加图片"),
    "addOval" : MessageLookupByLibrary.simpleMessage("添加椭圆"),
    "addParallelogram" : MessageLookupByLibrary.simpleMessage("添加平行四边形"),
    "addRectangle" : MessageLookupByLibrary.simpleMessage("添加矩形"),
    "addResizableRectangle" : MessageLookupByLibrary.simpleMessage("添加可变矩形"),
    "addStorage" : MessageLookupByLibrary.simpleMessage("添加存储"),
    "addToFlowchart" : MessageLookupByLibrary.simpleMessage("添加到流程图"),
    "addVariable" : MessageLookupByLibrary.simpleMessage("添加变量"),
    "addVideo" : MessageLookupByLibrary.simpleMessage("添加视频"),
    "adultAgreementContent" : MessageLookupByLibrary.simpleMessage("Steam创意工坊包含全球玩家的共创内容，其中可能涉及公共场所不宜观看的内容。"),
    "adultAgreementTitle" : MessageLookupByLibrary.simpleMessage("请确定您年满18周岁"),
    "ageRating" : MessageLookupByLibrary.simpleMessage("年龄评级"),
    "allSettings" : MessageLookupByLibrary.simpleMessage("所有设置"),
    "alreadyFavorited" : MessageLookupByLibrary.simpleMessage("已经收藏过了"),
    "alreadyLiked" : MessageLookupByLibrary.simpleMessage("已经点过赞了"),
    "alreadySubscribed" : MessageLookupByLibrary.simpleMessage("已订阅"),
    "apply" : MessageLookupByLibrary.simpleMessage("应用"),
    "archive" : MessageLookupByLibrary.simpleMessage("存档"),
    "archiveFileNotFound" : MessageLookupByLibrary.simpleMessage("存档文件不存在"),
    "archiveUpdatedForNode" : MessageLookupByLibrary.simpleMessage("已更新当前节点存档: "),
    "audioSettings" : MessageLookupByLibrary.simpleMessage("音频设置"),
    "audioTrack" : MessageLookupByLibrary.simpleMessage("音轨"),
    "autoFullScreenVideo" : MessageLookupByLibrary.simpleMessage("播放视频时自动全屏"),
    "autoSaveDescription" : MessageLookupByLibrary.simpleMessage("自动保存游戏进度"),
    "autoSaveGame" : MessageLookupByLibrary.simpleMessage("自动保存游戏"),
    "autoSaveInterval" : MessageLookupByLibrary.simpleMessage("自动保存间隔"),
    "autoSaveProgress" : MessageLookupByLibrary.simpleMessage("自动保存进度"),
    "autoSelect" : MessageLookupByLibrary.simpleMessage("自动选择"),
    "back" : MessageLookupByLibrary.simpleMessage("返回"),
    "backgroundColor" : MessageLookupByLibrary.simpleMessage("背景颜色"),
    "booleanType" : MessageLookupByLibrary.simpleMessage("布尔"),
    "borderColor" : MessageLookupByLibrary.simpleMessage("边框颜色"),
    "branchIndexLabel" : MessageLookupByLibrary.simpleMessage("分支 "),
    "branchSettingsSaved" : MessageLookupByLibrary.simpleMessage("已保存分支设置和流程图"),
    "branchWithText" : MessageLookupByLibrary.simpleMessage("分支: "),
    "buttonDisplayTime" : MessageLookupByLibrary.simpleMessage("按钮显示时间"),
    "buttonDisplayTimeDescription" : MessageLookupByLibrary.simpleMessage("视频结束前多少秒显示分支按钮"),
    "buttonDisplayTimeNote" : MessageLookupByLibrary.simpleMessage("设置为0时，视频播放结束才显示分支"),
    "buttonOpacity" : MessageLookupByLibrary.simpleMessage("按钮透明度"),
    "buttonPositionMiddle" : MessageLookupByLibrary.simpleMessage("%, 垂直位置: "),
    "buttonPositionPrefix" : MessageLookupByLibrary.simpleMessage("水平位置: "),
    "buttonPositionSuffix" : MessageLookupByLibrary.simpleMessage("%"),
    "buttonText" : MessageLookupByLibrary.simpleMessage("按钮文字"),
    "buttonTextOnly" : MessageLookupByLibrary.simpleMessage("仅显示文字（无按钮背景）"),
    "cancel" : MessageLookupByLibrary.simpleMessage("取消"),
    "captureCurrentFrame" : MessageLookupByLibrary.simpleMessage("捕获当前帧"),
    "challenge" : MessageLookupByLibrary.simpleMessage("挑战"),
    "challengeName" : MessageLookupByLibrary.simpleMessage("挑战的名称"),
    "clickToSubscribeAndDownload" : MessageLookupByLibrary.simpleMessage("点击订阅开始下载"),
    "close" : MessageLookupByLibrary.simpleMessage("关闭"),
    "closeSubtitle" : MessageLookupByLibrary.simpleMessage("关闭字幕"),
    "committingChanges" : MessageLookupByLibrary.simpleMessage("提交更改"),
    "completed" : MessageLookupByLibrary.simpleMessage("是否完成"),
    "confirm" : MessageLookupByLibrary.simpleMessage("确认"),
    "confirmExitApp" : MessageLookupByLibrary.simpleMessage("确定要退出应用吗？"),
    "confirmUnsubscribe" : MessageLookupByLibrary.simpleMessage("确定要取消订阅吗？"),
    "confirmUpdateWorkshopItem" : MessageLookupByLibrary.simpleMessage("更新创意工坊物品"),
    "confirmUpdateWorkshopItemDescription" : MessageLookupByLibrary.simpleMessage("您将要更新现有的创意工坊物品。这将用您的更改替换当前版本。"),
    "congratsEarnedCoins" : MessageLookupByLibrary.simpleMessage("恭喜！获得10金币奖励！"),
    "coverLoadFailed" : MessageLookupByLibrary.simpleMessage("封面加载失败"),
    "createChallenge" : MessageLookupByLibrary.simpleMessage("创建挑战"),
    "createNewGame" : MessageLookupByLibrary.simpleMessage("创建游戏"),
    "createTime" : MessageLookupByLibrary.simpleMessage("创建时间"),
    "credits" : MessageLookupByLibrary.simpleMessage("制作人员名单"),
    "creditsAnimationDesigner" : MessageLookupByLibrary.simpleMessage("动效设计师"),
    "creditsArtDesigner" : MessageLookupByLibrary.simpleMessage("美术设计师"),
    "creditsCodeWriter" : MessageLookupByLibrary.simpleMessage("代码编写"),
    "creditsCopywriter" : MessageLookupByLibrary.simpleMessage("文案编写"),
    "creditsCreativeSource" : MessageLookupByLibrary.simpleMessage("创意来源"),
    "creditsCreativeTitle" : MessageLookupByLibrary.simpleMessage("创意与策划"),
    "creditsGameProducer" : MessageLookupByLibrary.simpleMessage("游戏制作人"),
    "creditsGameplayPlanner" : MessageLookupByLibrary.simpleMessage("玩法策划"),
    "creditsMarketingTitle" : MessageLookupByLibrary.simpleMessage("市场宣发"),
    "creditsProgrammingTitle" : MessageLookupByLibrary.simpleMessage("程序开发"),
    "creditsSoftwareArchitect" : MessageLookupByLibrary.simpleMessage("软件架构"),
    "creditsSoftwarePlanner" : MessageLookupByLibrary.simpleMessage("软件策划"),
    "creditsSpecialThanks" : MessageLookupByLibrary.simpleMessage("特别鸣谢"),
    "creditsSubtitle" : MessageLookupByLibrary.simpleMessage("查看参与软件开发的团队和贡献者"),
    "creditsThanksTitle" : MessageLookupByLibrary.simpleMessage("感谢你们玩我的游戏！"),
    "creditsVideoProducer" : MessageLookupByLibrary.simpleMessage("视频制作"),
    "creditsVisualTitle" : MessageLookupByLibrary.simpleMessage("视觉设计"),
    "currentPosition" : MessageLookupByLibrary.simpleMessage("当前位置"),
    "curved" : MessageLookupByLibrary.simpleMessage("曲线"),
    "customButtonPosition" : MessageLookupByLibrary.simpleMessage("自定义按钮位置"),
    "delete" : MessageLookupByLibrary.simpleMessage("删除"),
    "description" : MessageLookupByLibrary.simpleMessage("描述"),
    "developer" : MessageLookupByLibrary.simpleMessage("开发者"),
    "diamondNode" : MessageLookupByLibrary.simpleMessage("菱形节点"),
    "disconnectedVideoCount" : MessageLookupByLibrary.simpleMessage("有视频未连入节点数: "),
    "diskSpaceInsufficient" : MessageLookupByLibrary.simpleMessage("磁盘空间不足，基于Steam上传机制，需要在软件所在磁盘保留足够空间。请清理磁盘空间后重新尝试上传。"),
    "divide" : MessageLookupByLibrary.simpleMessage("除以"),
    "download" : MessageLookupByLibrary.simpleMessage("下载"),
    "downloadFailed" : MessageLookupByLibrary.simpleMessage("下载失败"),
    "downloadSuccess" : MessageLookupByLibrary.simpleMessage("下载成功"),
    "downloadWorkshop" : MessageLookupByLibrary.simpleMessage("下载创意工坊"),
    "downloading" : MessageLookupByLibrary.simpleMessage("下载中"),
    "downloadingPleaseWait" : MessageLookupByLibrary.simpleMessage("正在下载请稍后"),
    "downloads" : MessageLookupByLibrary.simpleMessage("下载"),
    "duration" : MessageLookupByLibrary.simpleMessage("总共时长"),
    "editGlobalValue" : MessageLookupByLibrary.simpleMessage("编辑全局数值"),
    "editGlobalValueTitle" : MessageLookupByLibrary.simpleMessage("编辑全局数值: "),
    "elementId" : MessageLookupByLibrary.simpleMessage("ID"),
    "elementParams" : MessageLookupByLibrary.simpleMessage("元素参数"),
    "elementText" : MessageLookupByLibrary.simpleMessage("文本"),
    "elementType" : MessageLookupByLibrary.simpleMessage("类型"),
    "elevation" : MessageLookupByLibrary.simpleMessage("高度"),
    "enableFlowchartCheck" : MessageLookupByLibrary.simpleMessage("启用流程图检测"),
    "enableVideoClickPause" : MessageLookupByLibrary.simpleMessage("视频播放时点击可暂停播放"),
    "enableVideoClickPauseDesc" : MessageLookupByLibrary.simpleMessage("开启后，点击视频区域可以切换播放/暂停状态"),
    "endTime" : MessageLookupByLibrary.simpleMessage("结束\n时间"),
    "endTimeLabel" : MessageLookupByLibrary.simpleMessage("结束时间"),
    "endTimeNotExceedTotal" : MessageLookupByLibrary.simpleMessage("结束时间不能超过视频总长度"),
    "enterProjectName" : MessageLookupByLibrary.simpleMessage("输入项目名"),
    "equalTo" : MessageLookupByLibrary.simpleMessage("等于"),
    "exit" : MessageLookupByLibrary.simpleMessage("退出游戏"),
    "exitFullscreen" : MessageLookupByLibrary.simpleMessage("退出全屏"),
    "failedToGetWorkshopItems" : MessageLookupByLibrary.simpleMessage("获取创意工坊物品失败"),
    "favoriteFailed" : MessageLookupByLibrary.simpleMessage("收藏失败"),
    "favoriteSuccess" : MessageLookupByLibrary.simpleMessage("收藏成功"),
    "fileNotFound" : MessageLookupByLibrary.simpleMessage("文件不存在"),
    "fileNotFoundTitle" : MessageLookupByLibrary.simpleMessage("文件不存在"),
    "fileNotFoundWithPath" : MessageLookupByLibrary.simpleMessage("文件不存在: "),
    "filePreparingPleaseRetry" : MessageLookupByLibrary.simpleMessage("文件准备中，请稍后再试"),
    "flowChart" : MessageLookupByLibrary.simpleMessage("流程图"),
    "flowchart" : MessageLookupByLibrary.simpleMessage("回顾流程"),
    "flowchartCheckDescription" : MessageLookupByLibrary.simpleMessage("检测未连接节点和起点连接情况"),
    "flowchartFileNotFound" : MessageLookupByLibrary.simpleMessage("找不到FLOWCHART.json文件"),
    "flowchartLoadError" : MessageLookupByLibrary.simpleMessage("加载流程图时出错: "),
    "flowchartMissingStart" : MessageLookupByLibrary.simpleMessage("流程图缺少起点元素"),
    "friends" : MessageLookupByLibrary.simpleMessage("好友"),
    "friendsonly" : MessageLookupByLibrary.simpleMessage("好友可见"),
    "fullVideoPath" : MessageLookupByLibrary.simpleMessage("完整视频路径: "),
    "fullscreen" : MessageLookupByLibrary.simpleMessage("全屏模式"),
    "gallery" : MessageLookupByLibrary.simpleMessage("图库"),
    "gameTitle" : MessageLookupByLibrary.simpleMessage("互动视频游戏引擎"),
    "gameWindow" : MessageLookupByLibrary.simpleMessage("游戏窗口"),
    "games" : MessageLookupByLibrary.simpleMessage("游戏"),
    "generalBranchSettingsSaved" : MessageLookupByLibrary.simpleMessage("已保存分支设置和流程图"),
    "generalSettings" : MessageLookupByLibrary.simpleMessage("通用设置"),
    "globalValueName" : MessageLookupByLibrary.simpleMessage("数值名称"),
    "globalValueNameHint" : MessageLookupByLibrary.simpleMessage("例如：金币、生命值等"),
    "globalValues" : MessageLookupByLibrary.simpleMessage("全局数值"),
    "greaterThan" : MessageLookupByLibrary.simpleMessage("大于"),
    "greaterThanOrEqual" : MessageLookupByLibrary.simpleMessage("大于等于"),
    "hexagonNode" : MessageLookupByLibrary.simpleMessage("六边形节点"),
    "hideController" : MessageLookupByLibrary.simpleMessage("隐藏控制栏"),
    "home" : MessageLookupByLibrary.simpleMessage("主页"),
    "hour" : MessageLookupByLibrary.simpleMessage("时"),
    "ilpDesc" : MessageLookupByLibrary.simpleMessage("描述"),
    "ilpEditor" : MessageLookupByLibrary.simpleMessage("游戏编辑器"),
    "imageNode" : MessageLookupByLibrary.simpleMessage("图像节点"),
    "importImage" : MessageLookupByLibrary.simpleMessage("导入图片"),
    "initialValue" : MessageLookupByLibrary.simpleMessage("初始值"),
    "inputDescription" : MessageLookupByLibrary.simpleMessage("请输入描述"),
    "interfaceLanguage" : MessageLookupByLibrary.simpleMessage("界面语言"),
    "invalidEndTime" : MessageLookupByLibrary.simpleMessage("结束时间无效"),
    "invalidFileType" : MessageLookupByLibrary.simpleMessage("无效的文件类型"),
    "invalidProjectPath" : MessageLookupByLibrary.simpleMessage("项目路径无效"),
    "invalidStartTime" : MessageLookupByLibrary.simpleMessage("开始时间无效"),
    "invalidTimeFormat" : MessageLookupByLibrary.simpleMessage("时间格式无效"),
    "itemNotSubscribed" : MessageLookupByLibrary.simpleMessage("物品未订阅，订阅后会自动开始下载"),
    "joinDiscord" : MessageLookupByLibrary.simpleMessage("加入Discord参与游戏讨论"),
    "language" : MessageLookupByLibrary.simpleMessage("语言设置"),
    "languageSettings" : MessageLookupByLibrary.simpleMessage("语言设置"),
    "lastUpdate" : MessageLookupByLibrary.simpleMessage("最后更新"),
    "lessThan" : MessageLookupByLibrary.simpleMessage("小于"),
    "lessThanOrEqual" : MessageLookupByLibrary.simpleMessage("小于等于"),
    "likeFailed" : MessageLookupByLibrary.simpleMessage("点赞失败"),
    "likeSuccess" : MessageLookupByLibrary.simpleMessage("点赞成功"),
    "loadArchive" : MessageLookupByLibrary.simpleMessage("读取存档"),
    "loadArchiveError" : MessageLookupByLibrary.simpleMessage("加载存档时出错: "),
    "loadFlowchart" : MessageLookupByLibrary.simpleMessage("读取流程图"),
    "loadingFailed" : MessageLookupByLibrary.simpleMessage("加载失败"),
    "loadingFailedRetry" : MessageLookupByLibrary.simpleMessage("加载失败，点击重试"),
    "mainWindowFullscreen" : MessageLookupByLibrary.simpleMessage("主窗口全屏"),
    "maxValue" : MessageLookupByLibrary.simpleMessage("最大值"),
    "messages" : MessageLookupByLibrary.simpleMessage("消息"),
    "minValue" : MessageLookupByLibrary.simpleMessage("最小值"),
    "minute" : MessageLookupByLibrary.simpleMessage("分"),
    "modifyChapterTitle" : MessageLookupByLibrary.simpleMessage("修改章节标题"),
    "modifyTimeAndCover" : MessageLookupByLibrary.simpleMessage("修改时间与封面"),
    "multiply" : MessageLookupByLibrary.simpleMessage("乘以"),
    "myGames" : MessageLookupByLibrary.simpleMessage("我的游戏"),
    "nameBranchSettingsSaved" : MessageLookupByLibrary.simpleMessage("分支\"\"的条件和数值变化设置已保存"),
    "newProject" : MessageLookupByLibrary.simpleMessage("新建项目"),
    "newWindowFullScreen" : MessageLookupByLibrary.simpleMessage("新窗口全屏"),
    "newWindowFullScreenDescription" : MessageLookupByLibrary.simpleMessage("打开新窗口时自动设置为全屏模式"),
    "news" : MessageLookupByLibrary.simpleMessage("新闻"),
    "no" : MessageLookupByLibrary.simpleMessage("不需要"),
    "noArchivesFound" : MessageLookupByLibrary.simpleMessage("没有找到存档"),
    "noAudioTrack" : MessageLookupByLibrary.simpleMessage("无音轨"),
    "noBranchesToSet" : MessageLookupByLibrary.simpleMessage("当前节点没有多个分支，无法设置分支参数"),
    "noGlobalValuesFoundAddFirst" : MessageLookupByLibrary.simpleMessage("没有找到全局数值，请先在全局数值管理中添加数值"),
    "noSubtitle" : MessageLookupByLibrary.simpleMessage("无字幕"),
    "noValidFilesFound" : MessageLookupByLibrary.simpleMessage("没有找到有效的上传文件，请确保你的项目只包含支持的文件类型"),
    "noWorkshopItems" : MessageLookupByLibrary.simpleMessage("您没有可更新的创意工坊物品"),
    "nodeDetails" : MessageLookupByLibrary.simpleMessage("节点详情"),
    "nodeMarkedAsWatched" : MessageLookupByLibrary.simpleMessage("已标记节点为已观看: "),
    "normalBranch" : MessageLookupByLibrary.simpleMessage("常规分支"),
    "notEqual" : MessageLookupByLibrary.simpleMessage("不等于"),
    "numberType" : MessageLookupByLibrary.simpleMessage("数字"),
    "opaque" : MessageLookupByLibrary.simpleMessage("不透明"),
    "openFlowChartFailed" : MessageLookupByLibrary.simpleMessage("打开流程图失败: "),
    "openGame" : MessageLookupByLibrary.simpleMessage("打开游戏"),
    "openGameError" : MessageLookupByLibrary.simpleMessage("打开游戏项目出错: "),
    "openGameInNewWindow" : MessageLookupByLibrary.simpleMessage("用新窗口打开游戏项目"),
    "openGameInNewWindowDesc" : MessageLookupByLibrary.simpleMessage("启用后使用新窗口打开游戏项目，禁用则在当前窗口打开"),
    "openGameTitle" : MessageLookupByLibrary.simpleMessage("打开游戏"),
    "ovalNode" : MessageLookupByLibrary.simpleMessage("椭圆节点"),
    "parallelogramNode" : MessageLookupByLibrary.simpleMessage("平行四边形节点"),
    "pause" : MessageLookupByLibrary.simpleMessage("暂停"),
    "play" : MessageLookupByLibrary.simpleMessage("播放"),
    "playPause" : MessageLookupByLibrary.simpleMessage("播放/暂停"),
    "playTimeSettings" : MessageLookupByLibrary.simpleMessage("播放起止时间设置"),
    "playbackControl" : MessageLookupByLibrary.simpleMessage("播放控制"),
    "playbackProgress" : MessageLookupByLibrary.simpleMessage("播放进度"),
    "playbackRate" : MessageLookupByLibrary.simpleMessage("播放速率"),
    "playbackSpeed" : MessageLookupByLibrary.simpleMessage("播放速度"),
    "playbackStatus" : MessageLookupByLibrary.simpleMessage("播放状态"),
    "player" : MessageLookupByLibrary.simpleMessage("玩家"),
    "pleaseEnterTrueOrFalse" : MessageLookupByLibrary.simpleMessage("请输入true或false"),
    "pleaseEnterValidNumber" : MessageLookupByLibrary.simpleMessage("请输入有效数字"),
    "pleaseEnterValue" : MessageLookupByLibrary.simpleMessage("请输入数值"),
    "pleaseEnterValueName" : MessageLookupByLibrary.simpleMessage("请输入数值名称"),
    "pleaseEnterVariableName" : MessageLookupByLibrary.simpleMessage("请输入变量名"),
    "pleaseSelectProject" : MessageLookupByLibrary.simpleMessage("请先选择项目"),
    "popularGames" : MessageLookupByLibrary.simpleMessage("热门游戏"),
    "preparingConfig" : MessageLookupByLibrary.simpleMessage("准备配置"),
    "preparingProjectFiles" : MessageLookupByLibrary.simpleMessage("准备项目文件中..."),
    "previewImage" : MessageLookupByLibrary.simpleMessage("预览图片"),
    "previewImageDefault" : MessageLookupByLibrary.simpleMessage("默认"),
    "private" : MessageLookupByLibrary.simpleMessage("私有的"),
    "profile" : MessageLookupByLibrary.simpleMessage("个人资料"),
    "projectCreated" : MessageLookupByLibrary.simpleMessage("项目已创建"),
    "projectExistsContent" : MessageLookupByLibrary.simpleMessage("该项目已存在，是否进入该项目？"),
    "projectExistsTitle" : MessageLookupByLibrary.simpleMessage("项目已存在"),
    "projectLoaded" : MessageLookupByLibrary.simpleMessage("项目已加载: "),
    "projectNameHint" : MessageLookupByLibrary.simpleMessage("请输入项目名称"),
    "projectNotFound" : MessageLookupByLibrary.simpleMessage("未找到项目: "),
    "public" : MessageLookupByLibrary.simpleMessage("公开的"),
    "publicMode" : MessageLookupByLibrary.simpleMessage("公共场所模式/直播模式"),
    "publicModeDesc" : MessageLookupByLibrary.simpleMessage("启用后创意工坊界面将强制使用所有人的标签且不显示非工作场所的标签"),
    "publish" : MessageLookupByLibrary.simpleMessage("发布"),
    "publishTime" : MessageLookupByLibrary.simpleMessage("发布时间"),
    "puzzleHint" : MessageLookupByLibrary.simpleMessage("已经减少了一个错误答案"),
    "qteBranch" : MessageLookupByLibrary.simpleMessage("QTE分支"),
    "qteButtonDisplayTime" : MessageLookupByLibrary.simpleMessage("QTE\n"),
    "qteButtonDurationSeconds" : MessageLookupByLibrary.simpleMessage("按钮显示时间: "),
    "qteButtonPosition" : MessageLookupByLibrary.simpleMessage("QTE按钮位置"),
    "qteDuration" : MessageLookupByLibrary.simpleMessage("QTE按钮持续时间"),
    "qteDurationDescription" : MessageLookupByLibrary.simpleMessage("玩家需要在此时间内做出反应"),
    "qteFailBranch" : MessageLookupByLibrary.simpleMessage("QTE失败分支"),
    "qteFailLabel" : MessageLookupByLibrary.simpleMessage("失败"),
    "qtePositionInfo" : MessageLookupByLibrary.simpleMessage("QTE按钮位置: "),
    "qteSuccessBranch" : MessageLookupByLibrary.simpleMessage("QTE成功分支"),
    "qteSuccessLabel" : MessageLookupByLibrary.simpleMessage("成功"),
    "questionDescription" : MessageLookupByLibrary.simpleMessage("问题描述"),
    "range" : MessageLookupByLibrary.simpleMessage("范围"),
    "rate" : MessageLookupByLibrary.simpleMessage("播放速率"),
    "recentlyEdited" : MessageLookupByLibrary.simpleMessage("最近编辑"),
    "recentlyPlayed" : MessageLookupByLibrary.simpleMessage("最近玩过"),
    "rectangleNode" : MessageLookupByLibrary.simpleMessage("矩形节点"),
    "rectangular" : MessageLookupByLibrary.simpleMessage("矩形"),
    "remainingTimePrefix" : MessageLookupByLibrary.simpleMessage("剩余时间: "),
    "remainingTimeSuffix" : MessageLookupByLibrary.simpleMessage(" 秒"),
    "remoteReservedWord" : MessageLookupByLibrary.simpleMessage("remote为程序保留字，请更换一个项目名"),
    "removeAll" : MessageLookupByLibrary.simpleMessage("去除所有"),
    "removeAllConnections" : MessageLookupByLibrary.simpleMessage("去除所有连接"),
    "removeImage" : MessageLookupByLibrary.simpleMessage("移除图片"),
    "required" : MessageLookupByLibrary.simpleMessage("必填"),
    "resume" : MessageLookupByLibrary.simpleMessage("返回继续玩"),
    "retry" : MessageLookupByLibrary.simpleMessage("重试"),
    "retryLoading" : MessageLookupByLibrary.simpleMessage("点击重试"),
    "save" : MessageLookupByLibrary.simpleMessage("保存"),
    "saveFailed" : MessageLookupByLibrary.simpleMessage("保存失败，请检查日志"),
    "saveFlowchart" : MessageLookupByLibrary.simpleMessage("保存流程图"),
    "saveFlowchartFailed" : MessageLookupByLibrary.simpleMessage("保存流程图失败"),
    "saveSettings" : MessageLookupByLibrary.simpleMessage("保存设置"),
    "saving" : MessageLookupByLibrary.simpleMessage("保存中..."),
    "savingBranchSettings" : MessageLookupByLibrary.simpleMessage("正在保存分支设置..."),
    "screenPreview" : MessageLookupByLibrary.simpleMessage("屏幕预览"),
    "screenshots" : MessageLookupByLibrary.simpleMessage("截图"),
    "search" : MessageLookupByLibrary.simpleMessage("搜索"),
    "second" : MessageLookupByLibrary.simpleMessage("秒"),
    "seconds" : MessageLookupByLibrary.simpleMessage("秒"),
    "seed" : MessageLookupByLibrary.simpleMessage("种子"),
    "segmented" : MessageLookupByLibrary.simpleMessage("分段"),
    "selectBranchToSet" : MessageLookupByLibrary.simpleMessage("选择要设置的分支"),
    "selectContentRating" : MessageLookupByLibrary.simpleMessage("请选择内容评级"),
    "selectWorkshopItemToUpdate" : MessageLookupByLibrary.simpleMessage("选择要更新的创意工坊物品"),
    "setAndEnableConditions" : MessageLookupByLibrary.simpleMessage("设置并启用当前选项出现的条件"),
    "setAndEnableValueChanges" : MessageLookupByLibrary.simpleMessage("设置并启用选择选项后的数值变化"),
    "setBranchConditionsAndChanges" : MessageLookupByLibrary.simpleMessage("设置分支\"\"的条件和数值变化"),
    "setBranchParams" : MessageLookupByLibrary.simpleMessage("设定分支种类与参数"),
    "setCurrentTime" : MessageLookupByLibrary.simpleMessage("设置当前时间"),
    "setOptionsAndValueChanges" : MessageLookupByLibrary.simpleMessage("设置选项及数值变化"),
    "setTo" : MessageLookupByLibrary.simpleMessage("设为"),
    "settings" : MessageLookupByLibrary.simpleMessage("设置"),
    "settingsSaved" : MessageLookupByLibrary.simpleMessage("设置已保存"),
    "shape" : MessageLookupByLibrary.simpleMessage("图片形状"),
    "showController" : MessageLookupByLibrary.simpleMessage("显示控制栏"),
    "showControllerDuringPlayback" : MessageLookupByLibrary.simpleMessage("在视频播放时显示控制栏"),
    "showVideoController" : MessageLookupByLibrary.simpleMessage("显示视频控制栏"),
    "showVideoControls" : MessageLookupByLibrary.simpleMessage("显示视频控制栏"),
    "showVideoControlsDescription" : MessageLookupByLibrary.simpleMessage("在视频播放时显示控制栏"),
    "sort" : MessageLookupByLibrary.simpleMessage("排序"),
    "sortBy" : MessageLookupByLibrary.simpleMessage("排序方式"),
    "sortByFavorites" : MessageLookupByLibrary.simpleMessage("按收藏数"),
    "sortByPublishDate" : MessageLookupByLibrary.simpleMessage("发布时间"),
    "sortBySubscribers" : MessageLookupByLibrary.simpleMessage("按订阅数"),
    "sortByUpdateDate" : MessageLookupByLibrary.simpleMessage("更新时间"),
    "sortByVote" : MessageLookupByLibrary.simpleMessage("按点赞量"),
    "startElementIdNotFound" : MessageLookupByLibrary.simpleMessage("无法获取项目的起始元素ID"),
    "startGame" : MessageLookupByLibrary.simpleMessage("开始游戏"),
    "startNode" : MessageLookupByLibrary.simpleMessage("起始节点"),
    "startNodeVideoCount" : MessageLookupByLibrary.simpleMessage("起点后视频节点数应为1，当前: "),
    "startPoint" : MessageLookupByLibrary.simpleMessage("起点"),
    "startTime" : MessageLookupByLibrary.simpleMessage("开始\n时间"),
    "startTimeBeforeEndTime" : MessageLookupByLibrary.simpleMessage("开始时间必须早于结束时间"),
    "startTimeLabel" : MessageLookupByLibrary.simpleMessage("开始时间"),
    "steamAuthorOtherFiles" : MessageLookupByLibrary.simpleMessage("作者的其他文件"),
    "steamChallenge" : MessageLookupByLibrary.simpleMessage("开始挑战"),
    "steamGallery" : MessageLookupByLibrary.simpleMessage("Steam创意工坊图库"),
    "steamLimitedAccount" : MessageLookupByLibrary.simpleMessage("Steam受限账号"),
    "steamWorkshop" : MessageLookupByLibrary.simpleMessage("Steam创意工坊"),
    "stop" : MessageLookupByLibrary.simpleMessage("停止"),
    "storageNode" : MessageLookupByLibrary.simpleMessage("存储节点"),
    "style" : MessageLookupByLibrary.simpleMessage("图片风格"),
    "subscribeFailed" : MessageLookupByLibrary.simpleMessage("订阅失败: "),
    "subscribeSuccess" : MessageLookupByLibrary.simpleMessage("订阅成功，开始下载..."),
    "subtitle" : MessageLookupByLibrary.simpleMessage("字幕"),
    "subtract" : MessageLookupByLibrary.simpleMessage("减去"),
    "switchHorizontal" : MessageLookupByLibrary.simpleMessage("切换到水平布局"),
    "switchVertical" : MessageLookupByLibrary.simpleMessage("切换到垂直布局"),
    "textType" : MessageLookupByLibrary.simpleMessage("文本"),
    "thickness" : MessageLookupByLibrary.simpleMessage("厚度"),
    "timeEdit" : MessageLookupByLibrary.simpleMessage("时间编辑"),
    "timeLimit" : MessageLookupByLibrary.simpleMessage("限定时间 (秒)"),
    "timedBranch" : MessageLookupByLibrary.simpleMessage("限时分支"),
    "title" : MessageLookupByLibrary.simpleMessage("标题"),
    "titleCannotBeEmpty" : MessageLookupByLibrary.simpleMessage("标题不能为空"),
    "titlePositionMiddle" : MessageLookupByLibrary.simpleMessage("%, 垂直"),
    "titlePositionPrefix" : MessageLookupByLibrary.simpleMessage("问题描述位置: 水平"),
    "titlePositionSuffix" : MessageLookupByLibrary.simpleMessage("%"),
    "to" : MessageLookupByLibrary.simpleMessage("到"),
    "toggleConnectable" : MessageLookupByLibrary.simpleMessage("切换可连接"),
    "toggleResizable" : MessageLookupByLibrary.simpleMessage("切换可调整大小"),
    "toggleSortDirection" : MessageLookupByLibrary.simpleMessage("切换排序方向"),
    "transparent" : MessageLookupByLibrary.simpleMessage("透明"),
    "unknownNodeType" : MessageLookupByLibrary.simpleMessage("未知类型"),
    "unsubscribe" : MessageLookupByLibrary.simpleMessage("取消订阅"),
    "unsubscribeFailed" : MessageLookupByLibrary.simpleMessage("取消订阅失败"),
    "unsubscribeSuccess" : MessageLookupByLibrary.simpleMessage("取消订阅成功"),
    "unsupportedFileFormat" : MessageLookupByLibrary.simpleMessage("不支持的文件格式，创意工坊只允许视频、jpg和json文件"),
    "updateSuccess" : MessageLookupByLibrary.simpleMessage("更新成功！"),
    "updateTime" : MessageLookupByLibrary.simpleMessage("更新时间"),
    "updateWorkshop" : MessageLookupByLibrary.simpleMessage("更新创意工坊物品"),
    "updateWorkshopError" : MessageLookupByLibrary.simpleMessage("更新创意工坊错误: "),
    "updating" : MessageLookupByLibrary.simpleMessage("正在更新..."),
    "upload" : MessageLookupByLibrary.simpleMessage("上传"),
    "uploadFailed" : MessageLookupByLibrary.simpleMessage("上传失败"),
    "uploadFailedWithColon" : MessageLookupByLibrary.simpleMessage("上传失败: "),
    "uploadNow" : MessageLookupByLibrary.simpleMessage("立刻上传"),
    "uploadSuccess" : MessageLookupByLibrary.simpleMessage("上传成功"),
    "uploadWorkshop" : MessageLookupByLibrary.simpleMessage("上传创意工坊"),
    "uploading" : MessageLookupByLibrary.simpleMessage("上传中"),
    "uploadingContent" : MessageLookupByLibrary.simpleMessage("上传内容"),
    "uploadingPleaseWait" : MessageLookupByLibrary.simpleMessage("正在上传中，请稍后..."),
    "uploadingPreviewImage" : MessageLookupByLibrary.simpleMessage("上传预览图"),
    "useNewWindowForEditing" : MessageLookupByLibrary.simpleMessage("用新窗口打开编辑项目界面"),
    "useNewWindowForEditingDescription" : MessageLookupByLibrary.simpleMessage("启用后使用新窗口打开项目编辑器，禁用则在当前窗口打开"),
    "valueInputHint" : MessageLookupByLibrary.simpleMessage("请输入初始数值"),
    "valueLabel" : MessageLookupByLibrary.simpleMessage("数值"),
    "variableAlreadyExists" : MessageLookupByLibrary.simpleMessage("变量名已存在"),
    "variableName" : MessageLookupByLibrary.simpleMessage("变量名"),
    "variableType" : MessageLookupByLibrary.simpleMessage("类型"),
    "version" : MessageLookupByLibrary.simpleMessage("版本"),
    "verticalLayoutDescription" : MessageLookupByLibrary.simpleMessage("视频编辑界面竖置（推荐高分辨率屏使用）"),
    "videoCover" : MessageLookupByLibrary.simpleMessage("视频封面"),
    "videoFileNotExist" : MessageLookupByLibrary.simpleMessage("视频文件不存在: "),
    "videoNode" : MessageLookupByLibrary.simpleMessage("视频节点"),
    "videoNodeLocked" : MessageLookupByLibrary.simpleMessage("此视频节点尚未解锁，请先观看前面的视频"),
    "videoPlayback" : MessageLookupByLibrary.simpleMessage("视频播放"),
    "videoPlaybackError" : MessageLookupByLibrary.simpleMessage("视频播放错误: "),
    "videoTimeAndCover" : MessageLookupByLibrary.simpleMessage("视频时间与封面"),
    "viewFlowChart" : MessageLookupByLibrary.simpleMessage("查看流程图"),
    "viewFlowchart" : MessageLookupByLibrary.simpleMessage("查看流程图"),
    "visibility" : MessageLookupByLibrary.simpleMessage("可见性"),
    "volume" : MessageLookupByLibrary.simpleMessage("音量大小"),
    "volumeControl" : MessageLookupByLibrary.simpleMessage("音量控制"),
    "watchedNodesCount" : MessageLookupByLibrary.simpleMessage("已观看节点数"),
    "workshop" : MessageLookupByLibrary.simpleMessage("创意工坊"),
    "workshopItemUpdated" : MessageLookupByLibrary.simpleMessage("创意工坊项目已更新"),
    "workshopItemUploaded" : MessageLookupByLibrary.simpleMessage("创意工坊项目已上传"),
    "workshopItems" : MessageLookupByLibrary.simpleMessage("创意工坊项目"),
    "workshopRecommendedDescription" : MessageLookupByLibrary.simpleMessage("由引擎村长亲自编写，村民都说好，本说明书会随着版本更新自动进化"),
    "workshopRecommendedTitle" : MessageLookupByLibrary.simpleMessage("拯救世界指南"),
    "workshopRuleNoAds" : MessageLookupByLibrary.simpleMessage("没有广告"),
    "workshopRuleNoAdult" : MessageLookupByLibrary.simpleMessage("没有照片形式或真实的色情或裸露内容"),
    "workshopRuleNoCopyright" : MessageLookupByLibrary.simpleMessage("没有侵犯版权"),
    "workshopRuleNoMisleading" : MessageLookupByLibrary.simpleMessage("没有误导性预览图"),
    "workshopRuleNoOffensive" : MessageLookupByLibrary.simpleMessage("没有冒犯性内容或者暴力、血腥内容"),
    "workshopRules" : MessageLookupByLibrary.simpleMessage("创意工坊规则"),
    "workshopRulesDescription" : MessageLookupByLibrary.simpleMessage("在将互动视频提交到创意工坊之前，请确保其没有违反 Steam 的服务条款，否则互动视频将会被移除："),
    "workshopRulesSpecial" : MessageLookupByLibrary.simpleMessage("特别是互动视频、预览图和描述更应遵守以下规则：")
  };
}
