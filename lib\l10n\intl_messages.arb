{"@@last_modified": "2025-06-03T23:22:26.679080", "gameTitle": "Interactive Video Engine", "@gameTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "settings": "Settings", "@settings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "allSettings": "All Settings", "@allSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "credits": "Credits", "@credits": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsCreativeTitle": "Creative & Planning", "@creditsCreativeTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsGameProducer": "Game Producer", "@creditsGameProducer": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsCreativeSource": "Creative Source", "@creditsCreativeSource": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsSoftwarePlanner": "Software Planner", "@creditsSoftwarePlanner": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsGameplayPlanner": "Gameplay Planner", "@creditsGameplayPlanner": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsProgrammingTitle": "Programming", "@creditsProgrammingTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsSoftwareArchitect": "Software Architect", "@creditsSoftwareArchitect": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsCodeWriter": "Code Writer", "@creditsCodeWriter": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsVisualTitle": "Visual Design", "@creditsVisualTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsArtDesigner": "Art Designer", "@creditsArtDesigner": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsAnimationDesigner": "Animation Designer", "@creditsAnimationDesigner": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsMarketingTitle": "Marketing", "@creditsMarketingTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsVideoProducer": "Video Producer", "@creditsVideoProducer": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsCopywriter": "Copy<PERSON>", "@creditsCopywriter": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsThanksTitle": "Thanks for playing my game!", "@creditsThanksTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsSpecialThanks": "Special Thanks", "@creditsSpecialThanks": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "creditsSubtitle": "View teams and contributors involved in software development", "@creditsSubtitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openGameInNewWindow": "Open Game in New Window", "@openGameInNewWindow": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openGameInNewWindowDesc": "When enabled, open game projects in a new window, otherwise open them in the current window", "@openGameInNewWindowDesc": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "publicMode": "Public Mode/Live Streaming Mode", "@publicMode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "publicModeDesc": "When enabled, the workshop interface will force the use of 'Everyone' content rating and not show NSFW tags", "@publicModeDesc": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "verticalLayoutDescription": "Vertical video editing interface (recommended for high resolution screens)", "@verticalLayoutDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "language": "Language", "@language": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "fullscreen": "Fullscreen Mode", "@fullscreen": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "mainWindowFullscreen": "Main Window Fullscreen", "@mainWindowFullscreen": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "createNewGame": "Create Game", "@createNewGame": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshop": "Workshop", "@workshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openGame": "Open Game", "@openGame": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "home": "Home", "@home": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "activity": "Activity", "@activity": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "profile": "Profile", "@profile": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloads": "Downloads", "@downloads": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "friends": "Friends", "@friends": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "messages": "Messages", "@messages": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "elementParams": "Element Parameters", "@elementParams": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "backgroundColor": "Background Color", "@backgroundColor": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "thickness": "<PERSON><PERSON><PERSON><PERSON>", "@thickness": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "borderColor": "Border Color", "@borderColor": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "elevation": "Elevation", "@elevation": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoPlayback": "Video Playback", "@videoPlayback": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playbackControl": "Playback Control", "@playbackControl": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "play": "Play", "@play": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pause": "Pause", "@pause": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "stop": "Stop", "@stop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playbackRate": "Playback Rate", "@playbackRate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoCover": "Video Cover", "@videoCover": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "captureCurrentFrame": "Capture Current Frame", "@captureCurrentFrame": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "timeEdit": "Time Edit", "@timeEdit": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startTime": "Start Time", "@startTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "endTime": "End Time", "@endTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setCurrentTime": "Set Current Time", "@setCurrentTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addVideo": "Add Video", "@addVideo": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addDiamond": "Add Diamond", "@addDiamond": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addRectangle": "Add Rectangle", "@addRectangle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addResizableRectangle": "Add Resizable Rectangle", "@addResizableRectangle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addOval": "Add Oval", "@addOval": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addParallelogram": "Add Parallelogram", "@addParallelogram": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addHexagon": "Add Hexagon", "@addHexagon": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addStorage": "Add Storage", "@addStorage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addImage": "Add Image", "@addImage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "removeAll": "Remove All", "@removeAll": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "saveFlowchart": "Save Flowchart", "@saveFlowchart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "loadFlowchart": "Load Flowchart", "@loadFlowchart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopItems": "Workshop Items", "@workshopItems": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "loadingFailed": "Loading Failed", "@loadingFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "retryLoading": "Click to Retry", "@retryLoading": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openGameTitle": "Open Game", "@openGameTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "showController": "Show Controller", "@showController": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "hideController": "Hide Controller", "@hideController": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "myGames": "My Games", "@myGames": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "popularGames": "Popular Games", "@popularGames": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "news": "News", "@news": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "recentlyPlayed": "Recently Played", "@recentlyPlayed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "recentlyEdited": "Recently Edited", "@recentlyEdited": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "achievementDisplay": "Achievement Display", "@achievementDisplay": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "achievements": "Achievements", "@achievements": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "screenshots": "Screenshots", "@screenshots": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "player": "Player", "@player": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "games": "Games", "@games": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "search": "Search", "@search": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "modifyTimeAndCover": "Modify Time and Cover", "@modifyTimeAndCover": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "delete": "Delete", "@delete": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "removeAllConnections": "Remove All Connections", "@removeAllConnections": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "toggleConnectable": "Toggle Connectable", "@toggleConnectable": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "toggleResizable": "Toggle Resizable", "@toggleResizable": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "segmented": "Segmented", "@segmented": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "curved": "Curved", "@curved": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "rectangular": "Rectangular", "@rectangular": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startPoint": "Start", "@startPoint": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "loadingFailedRetry": "Loading Failed, Click to Retry", "@loadingFailedRetry": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowchartMissingStart": "Flowchart missing start element", "@flowchartMissingStart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoTimeAndCover": "Video Time and Cover", "@videoTimeAndCover": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "coverLoadFailed": "Cover Load Failed", "@coverLoadFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "close": "Close", "@close": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "confirm": "Confirm", "@confirm": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playPause": "Play/Pause", "@playPause": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "invalidStartTime": "Invalid Start Time", "@invalidStartTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "invalidEndTime": "Invalid End Time", "@invalidEndTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playbackStatus": "Playback Status", "@playbackStatus": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "currentPosition": "Current Position", "@currentPosition": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "volume": "Volume Level", "@volume": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "completed": "Completion Status", "@completed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "duration": "Total Duration", "@duration": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "rate": "Playback Rate", "@rate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playbackProgress": "Playback Progress", "@playbackProgress": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "volumeControl": "Volume Control", "@volumeControl": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "audioTrack": "Audio Track", "@audioTrack": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "subtitle": "Subtitle", "@subtitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noSubtitle": "No Subtitle", "@noSubtitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "closeSubtitle": "Close Subtitle", "@closeSubtitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "modifyChapterTitle": "Modify Chapter Title", "@modifyChapterTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setBranchParams": "Set Branch Type and Parameters", "@setBranchParams": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noAudioTrack": "No AudioTrack", "@noAudioTrack": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "enterProjectName": "Enter Project Name", "@enterProjectName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectNameHint": "Project Name", "@projectNameHint": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "cancel": "Cancel", "@cancel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectCreated": "Project Created Successfully", "@projectCreated": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "fileNotFound": "File Not Found", "@fileNotFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "upload": "Upload", "@upload": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadWorkshop": "Upload to Workshop", "@uploadWorkshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "importImage": "Import Image", "@importImage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "removeImage": "Remove Image", "@removeImage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "previewImageDefault": "<PERSON><PERSON><PERSON>", "@previewImageDefault": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "ageRating": "Age Rating", "@ageRating": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "visibility": "Visibility", "@visibility": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadNow": "Upload Now", "@uploadNow": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploading": "Uploading", "@uploading": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadFailed": "Upload Failed", "@uploadFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadSuccess": "Upload Success", "@uploadSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "download": "Download", "@download": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloadWorkshop": "Download from Workshop", "@downloadWorkshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloading": "Downloading", "@downloading": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloadFailed": "Download Failed", "@downloadFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloadSuccess": "Download Success", "@downloadSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "invalidFileType": "Invalid File Type", "@invalidFileType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "unsupportedFileFormat": "Unsupported file format, workshop only allows video, jpg and json files", "@unsupportedFileFormat": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noValidFilesFound": "No valid files found for upload, please ensure your project includes supported files only", "@noValidFilesFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "SearchWorkshop": "Search in the workshop", "@SearchWorkshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "style": "Style", "@style": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "AncientChinese": "Ancient Chinese", "@AncientChinese": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "shape": "<PERSON><PERSON><PERSON>", "@shape": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "ilpEditor": "Game Editor", "@ilpEditor": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "steamLimitedAccount": "Steam Limited Account", "@steamLimitedAccount": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sort": "Sort", "@sort": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "joinDiscord": "Join Discord to discussion", "@joinDiscord": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "back": "Back", "@back": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "no": "No", "@no": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "resume": "Resume", "@resume": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "retry": "Retry", "@retry": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "seed": "Seed", "@seed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "about": "About", "@about": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "exit": "Exit", "@exit": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "confirmExitApp": "Are you sure you want to exit the application?", "@confirmExitApp": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "challenge": "Challenges", "@challenge": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "gallery": "Gallery", "@gallery": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "steamChallenge": "Start challenge", "@steamChallenge": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "steamGallery": "Steam workshop gallery", "@steamGallery": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Subscribe": "Subscribe", "@Subscribe": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "SubscribeAndDownload": "Subscribe & download", "@SubscribeAndDownload": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "clickToSubscribeAndDownload": "Click to subscribe and download", "@clickToSubscribeAndDownload": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "createChallenge": "Create challenge", "@createChallenge": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "challengeName": "Challenge name", "@challengeName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "ilpDesc": "Description", "@ilpDesc": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Everyone": "Everyone", "@Everyone": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "NSFW": "NSFW", "@NSFW": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "public": "Public", "@public": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "friendsonly": "Friends Only", "@friendsonly": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "private": "Private", "@private": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Anime": "Anime", "@Anime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Realistic": "Realistic", "@Realistic": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Pixel": "Pixel", "@Pixel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Other": "Other", "@Other": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Landscape": "Landscape", "@Landscape": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Portrait": "Portrait", "@Portrait": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Square": "Square", "@Square": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "puzzleHint": "Already reduced one wrong answer", "@puzzleHint": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "steamWorkshop": "Steam Workshop", "@steamWorkshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "steamAuthorOtherFiles": "Author's Other Files", "@steamAuthorOtherFiles": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "publishTime": "Publish Time", "@publishTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "updateTime": "Update Time", "@updateTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "adultAgreementTitle": "Please make sure you are over 18 years old", "@adultAgreementTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "adultAgreementContent": "The Steam Workshop contains co created content from players worldwide, which may involve content that is not suitable for viewing in public places.", "@adultAgreementContent": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Unsubscribed": "Unsubscribed", "@Unsubscribed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "itemNotSubscribed": "Item not subscribed, it will start downloading automatically after subscription", "@itemNotSubscribed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "subscribeSuccess": "Subscribe success, downloading...", "@subscribeSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "subscribeFailed": "Subscribe failed: ", "@subscribeFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "downloadingPleaseWait": "Downloading, please wait", "@downloadingPleaseWait": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "Subscribed": "Subscribed", "@Subscribed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortBySubscribers": "By Subscribers", "@sortBySubscribers": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortByVote": "By Votes", "@sortByVote": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortByFavorites": "By Favorites", "@sortByFavorites": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortByPublishDate": "By Publish Date", "@sortByPublishDate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortByUpdateDate": "By Update Date", "@sortByUpdateDate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "sortBy": "Sort By", "@sortBy": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "toggleSortDirection": "Toggle Sort Direction", "@toggleSortDirection": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoFullScreenVideo": "Auto Fullscreen Video", "@autoFullScreenVideo": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "newWindowFullScreen": "New Window Fullscreen", "@newWindowFullScreen": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "newWindowFullScreenDescription": "When opening a new window, make it fullscreen automatically", "@newWindowFullScreenDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "filePreparingPleaseRetry": "File preparing, please try again later", "@filePreparingPleaseRetry": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "remoteReservedWord": "'remote' is a reserved word, please use a different project name", "@remoteReservedWord": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRules": "Workshop Rules", "@workshopRules": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRulesDescription": "Before submitting interactive videos to the creative workshop, please ensure that they do not violate Steam's terms of service, otherwise the interactive videos will be removed:", "@workshopRulesDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRulesSpecial": "Especially interactive videos, preview images and descriptions should follow these rules:", "@workshopRulesSpecial": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRuleNoAdult": "No photographic or realistic pornographic or nude content", "@workshopRuleNoAdult": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRuleNoOffensive": "No offensive content or violent, bloody content", "@workshopRuleNoOffensive": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRuleNoCopyright": "No copyright infringement", "@workshopRuleNoCopyright": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRuleNoAds": "No advertisements", "@workshopRuleNoAds": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRuleNoMisleading": "No misleading preview images", "@workshopRuleNoMisleading": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "archive": "Archive", "@archive": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowchart": "Flowchart", "@flowchart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startGame": "START GAME", "@startGame": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "viewFlowchart": "View Flowchart", "@viewFlowchart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "nodeDetails": "Node Details", "@nodeDetails": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "elementId": "ID", "@elementId": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "elementType": "Type", "@elementType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "elementText": "Text", "@elementText": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startNode": "Start Node", "@startNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoNode": "Video Node", "@videoNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "rectangleNode": "Rectangle Node", "@rectangleNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "diamondNode": "Diamond Node", "@diamondNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "storageNode": "Storage Node", "@storageNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "ovalNode": "Oval Node", "@ovalNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "parallelogramNode": "Parallelogram Node", "@parallelogramNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "hexagonNode": "Hexagon Node", "@hexagonNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "imageNode": "Image Node", "@imageNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "unknownNodeType": "Unknown Type", "@unknownNodeType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowchartFileNotFound": "FLOWCHART.json file not found", "@flowchartFileNotFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowchartLoadError": "Error loading flowchart: ", "@flowchartLoadError": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "createTime": "Create Time", "@createTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "lastUpdate": "Last Update", "@lastUpdate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "watchedNodesCount": "Watched Nodes Count", "@watchedNodesCount": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startElementIdNotFound": "Cannot get project's start element ID", "@startElementIdNotFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "archiveFileNotFound": "Archive file does not exist", "@archiveFileNotFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "loadArchiveError": "Error loading archive: ", "@loadArchiveError": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noArchivesFound": "No archives found", "@noArchivesFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "settingsSaved": "Setting<PERSON> saved", "@settingsSaved": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "saveSettings": "Save settings", "@saveSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "generalSettings": "General Settings", "@generalSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "audioSettings": "Audio Settings", "@audioSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "languageSettings": "Language Settings", "@languageSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoSaveProgress": "Auto save progress", "@autoSaveProgress": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoSaveDescription": "Automatically save game progress", "@autoSaveDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "showVideoControls": "Show video controls", "@showVideoControls": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "showVideoControlsDescription": "Show controller when playing video", "@showVideoControlsDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "interfaceLanguage": "Interface language", "@interfaceLanguage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "version": "Version", "@version": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "developer": "Developer", "@developer": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openGameError": "Error opening game: ", "@openGameError": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "alreadySubscribed": "Already subscribed", "@alreadySubscribed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoNodeLocked": "This video node has not been unlocked yet. Please watch the previous video first", "@videoNodeLocked": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "likeSuccess": "Like success", "@likeSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "alreadyLiked": "Already liked", "@alreadyLiked": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "likeFailed": "Like failed", "@likeFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "favoriteSuccess": "Favorite success", "@favoriteSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "alreadyFavorited": "Already favorited", "@alreadyFavorited": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "favoriteFailed": "Favorite failed", "@favoriteFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "unsubscribe": "Unsubscribe", "@unsubscribe": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "confirmUnsubscribe": "Are you sure you want to unsubscribe?", "@confirmUnsubscribe": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "unsubscribeSuccess": "Unsubscribe success", "@unsubscribeSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "unsubscribeFailed": "Unsubscribe failed", "@unsubscribeFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "title": "Title", "@title": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "required": "Required", "@required": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "description": "Description", "@description": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "inputDescription": "Input description", "@inputDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "selectContentRating": "Select Content Rating", "@selectContentRating": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "previewImage": "Preview Image", "@previewImage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "publish": "Publish", "@publish": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "titleCannotBeEmpty": "Title cannot be empty", "@titleCannotBeEmpty": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowChart": "Flow Chart", "@flowChart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseSelectProject": "Please select a project first", "@pleaseSelectProject": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "fileNotFoundTitle": "File not found", "@fileNotFoundTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "openFlowChartFailed": "Failed to open flow chart: ", "@openFlowChartFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "invalidTimeFormat": "Invalid time format", "@invalidTimeFormat": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startTimeBeforeEndTime": "Start time must be earlier than end time", "@startTimeBeforeEndTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "endTimeNotExceedTotal": "End time cannot exceed the total video length", "@endTimeNotExceedTotal": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "save": "Save", "@save": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playTimeSettings": "Play Start and End Time Settings", "@playTimeSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startTimeLabel": "Start Time", "@startTimeLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "endTimeLabel": "End Time", "@endTimeLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "hour": "Hour", "@hour": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "minute": "Minute", "@minute": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "second": "Second", "@second": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "showVideoController": "Show video controller", "@showVideoController": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "showControllerDuringPlayback": "Show controller during video playback", "@showControllerDuringPlayback": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "playbackSpeed": "Playback Speed", "@playbackSpeed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "fileNotFoundWithPath": "File not found: ", "@fileNotFoundWithPath": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "loadArchive": "Load Archive", "@loadArchive": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "viewFlowChart": "View Flow Chart", "@viewFlowChart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "switchHorizontal": "Switch to horizontal layout", "@switchHorizontal": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "switchVertical": "Switch to vertical layout", "@switchVertical": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "invalidProjectPath": "Invalid project path", "@invalidProjectPath": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "gameWindow": "Game Window", "@gameWindow": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectLoaded": "Project loaded: ", "@projectLoaded": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectNotFound": "Project not found: ", "@projectNotFound": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "newProject": "New Project", "@newProject": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectExistsTitle": "Project Exists", "@projectExistsTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "projectExistsContent": "This project already exists, do you want to enter this project?", "@projectExistsContent": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "confirmUpdateWorkshopItem": "Update Workshop Item", "@confirmUpdateWorkshopItem": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "confirmUpdateWorkshopItemDescription": "You are about to update an existing workshop item. This will replace the current version with your changes.", "@confirmUpdateWorkshopItemDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "updating": "Updating...", "@updating": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "updateSuccess": "Update successful!", "@updateSuccess": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "updateWorkshop": "Update Workshop Item", "@updateWorkshop": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noWorkshopItems": "You have no workshop items to update", "@noWorkshopItems": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "selectWorkshopItemToUpdate": "Select Workshop Item to Update", "@selectWorkshopItemToUpdate": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "updateWorkshopError": "Error updating workshop item: ", "@updateWorkshopError": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoSaveGame": "Auto save game", "@autoSaveGame": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoSaveInterval": "Auto save interval", "@autoSaveInterval": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "enableFlowchartCheck": "Enable Flowchart Check", "@enableFlowchartCheck": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "flowchartCheckDescription": "Detect disconnected nodes and start point connections", "@flowchartCheckDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "disconnectedVideoCount": "Disconnected video nodes: ", "@disconnectedVideoCount": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "startNodeVideoCount": "Start node should connect to 1 video, current: ", "@startNodeVideoCount": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "exitFullscreen": "Exit Fullscreen", "@exitFullscreen": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "useNewWindowForEditing": "Open Project in New Window", "@useNewWindowForEditing": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "useNewWindowForEditingDescription": "When enabled, project editor will open in a new window, otherwise it will open in the current window", "@useNewWindowForEditingDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopItemUpdated": "Workshop item updated", "@workshopItemUpdated": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopItemUploaded": "Workshop item uploaded", "@workshopItemUploaded": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadFailedWithColon": "Upload failed: ", "@uploadFailedWithColon": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "congratsEarnedCoins": "Congratulations! You earned 10 coins!", "@congratsEarnedCoins": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "preparingProjectFiles": "Preparing project files...", "@preparingProjectFiles": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "preparingConfig": "Preparing config", "@preparingConfig": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadingPleaseWait": "Uploading, please wait...", "@uploadingPleaseWait": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadingContent": "Uploading content", "@uploadingContent": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "uploadingPreviewImage": "Uploading preview image", "@uploadingPreviewImage": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "committingChanges": "Committing changes", "@committingChanges": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "diskSpaceInsufficient": "Insufficient disk space. Based on the Steam upload mechanism, adequate space is required on the disk where the application is located. Please free up disk space and try uploading again.", "@diskSpaceInsufficient": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noBranchesToSet": "No branches to set", "@noBranchesToSet": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "normalBranch": "Normal Branch", "@normalBranch": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "timedBranch": "Timed Branch", "@timedBranch": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteBranch": "QTE Branch", "@qteBranch": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "timeLimit": "Time Limit (seconds)", "@timeLimit": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "autoSelect": "Auto Select", "@autoSelect": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "questionDescription": "Question Description", "@questionDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonOpacity": "Button Opacity", "@buttonOpacity": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "transparent": "Transparent", "@transparent": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "opaque": "Opaque", "@opaque": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonText": "Button Text", "@buttonText": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "branchIndexLabel": "Branch ", "@branchIndexLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "customButtonPosition": "Custom Button Position", "@customButtonPosition": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "screenPreview": "Screen Preview", "@screenPreview": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "remainingTimePrefix": "Remaining Time: ", "@remainingTimePrefix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "remainingTimeSuffix": " seconds", "@remainingTimeSuffix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonPositionPrefix": "Horizontal: ", "@buttonPositionPrefix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonPositionMiddle": "%, Vertical: ", "@buttonPositionMiddle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonPositionSuffix": "%", "@buttonPositionSuffix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "titlePositionPrefix": "Title Position: Horizontal ", "@titlePositionPrefix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "titlePositionMiddle": "%, Vertical ", "@titlePositionMiddle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "titlePositionSuffix": "%", "@titlePositionSuffix": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "saving": "Saving...", "@saving": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "branchSettingsSaved": "Branch settings and flowchart saved", "@branchSettingsSaved": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "saveFlowchartFailed": "Save flowchart failed", "@saveFlowchartFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "fullVideoPath": "Full video path: ", "@fullVideoPath": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoFileNotExist": "Video file does not exist: ", "@videoFileNotExist": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "archiveUpdatedForNode": "Archive updated for node: ", "@archiveUpdatedForNode": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "nodeMarkedAsWatched": "Node marked as watched: ", "@nodeMarkedAsWatched": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "videoPlaybackError": "Video playback error: ", "@videoPlaybackError": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addGlobalValue": "Add Global Value", "@addGlobalValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "editGlobalValue": "Edit Global Value", "@editGlobalValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "globalValueName": "Value Name", "@globalValueName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "globalValueNameHint": "Example: Coins, Health, etc.", "@globalValueNameHint": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "initialValue": "Initial Value", "@initialValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "valueInputHint": "Please enter initial value", "@valueInputHint": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseEnterValueName": "Please enter a value name", "@pleaseEnterValueName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "editGlobalValueTitle": "Edit Global Value: ", "@editGlobalValueTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "valueLabel": "Value", "@valueLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "globalValues": "Global Values", "@globalValues": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "variableName": "Variable Name", "@variableName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseEnterVariableName": "Please enter variable name", "@pleaseEnterVariableName": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "variableAlreadyExists": "Variable already exists", "@variableAlreadyExists": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "numberType": "Number", "@numberType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "textType": "Text", "@textType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "booleanType": "Boolean", "@booleanType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "variableType": "Type", "@variableType": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addToFlowchart": "Add to Flowchart", "@addToFlowchart": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "addVariable": "Add Variable", "@addVariable": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseEnterValidNumber": "Please enter a valid number", "@pleaseEnterValidNumber": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseEnterTrueOrFalse": "Please enter true or false", "@pleaseEnterTrueOrFalse": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "pleaseEnterValue": "Please enter a value", "@pleaseEnterValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "failedToGetWorkshopItems": "Failed to get workshop items", "@failedToGetWorkshopItems": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRecommendedTitle": "World Saving Guide", "@workshopRecommendedTitle": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "workshopRecommendedDescription": "Written by the village chief of this engine, villagers all approve, this guide will automatically evolve with version updates", "@workshopRecommendedDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setOptionsAndValueChanges": "Set Options and Value Changes", "@setOptionsAndValueChanges": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "selectBranchToSet": "Select Branch to Set", "@selectBranchToSet": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "branchWithText": "Branch: ", "@branchWithText": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "noGlobalValuesFoundAddFirst": "No global values found, please add values in global value management first", "@noGlobalValuesFoundAddFirst": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "greaterThan": "Greater Than", "@greaterThan": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "lessThan": "Less Than", "@lessThan": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "equalTo": "Equal To", "@equalTo": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "greaterThanOrEqual": "Greater Than or Equal", "@greaterThanOrEqual": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "lessThanOrEqual": "Less Than or Equal", "@lessThanOrEqual": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "notEqual": "Not Equal", "@notEqual": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "range": "Range", "@range": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "add": "Add", "@add": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "subtract": "Subtract", "@subtract": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "multiply": "Multiply", "@multiply": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "divide": "Divide", "@divide": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setTo": "Set To", "@setTo": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setBranchConditionsAndChanges": "Set Branch \"\" Conditions and Value Changes", "@setBranchConditionsAndChanges": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setAndEnableConditions": "Set and Enable Conditions for Current Option", "@setAndEnableConditions": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "minValue": "Min Value", "@minValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "to": "to", "@to": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "maxValue": "Max Value", "@maxValue": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "setAndEnableValueChanges": "Set and Enable Value Changes After Selecting Option", "@setAndEnableValueChanges": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "savingBranchSettings": "Saving branch settings...", "@savingBranchSettings": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "nameBranchSettingsSaved": "Branch \"\" conditions and value changes saved", "@nameBranchSettingsSaved": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "generalBranchSettingsSaved": "Branch settings and flowchart saved", "@generalBranchSettingsSaved": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "saveFailed": "Save failed, please check logs", "@saveFailed": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "apply": "Apply", "@apply": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonDisplayTime": "Button Display Time", "@buttonDisplayTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonDisplayTimeDescription": "How many seconds before video ends to show branch buttons", "@buttonDisplayTimeDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonDisplayTimeNote": "When set to 0, buttons show only when video ends", "@buttonDisplayTimeNote": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "seconds": "seconds", "@seconds": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "buttonTextOnly": "Text Only (No But<PERSON> Background)", "@buttonTextOnly": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteDuration": "QTE Button Duration", "@qteDuration": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteDurationDescription": "Time for player to react", "@qteDurationDescription": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteSuccessBranch": "QTE Success Branch", "@qteSuccessBranch": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteFailBranch": "QTE Fail Branch", "@qteFailBranch": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteButtonPosition": "QTE Button Position", "@qteButtonPosition": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteButtonDurationSeconds": "<PERSON>ton Display Time: ", "@qteButtonDurationSeconds": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteButtonDisplayTime": "QTE\n", "@qteButtonDisplayTime": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteSuccessLabel": "Success", "@qteSuccessLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qteFailLabel": "Fail", "@qteFailLabel": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "qtePositionInfo": "QTE Button Position: ", "@qtePositionInfo": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "enableVideoClickPause": "Enable click to pause video", "@enableVideoClickPause": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}, "enableVideoClickPauseDesc": "When enabled, clicking the video area will toggle play/pause state", "@enableVideoClickPauseDesc": {"description": "", "type": "text", "placeholders_order": [], "placeholders": {}}}