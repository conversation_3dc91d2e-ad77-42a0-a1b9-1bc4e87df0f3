import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ve/extensions/size_extension.dart';
import 'package:ve/generated/l10n.dart';
import 'dart:io';
import 'package:steamworks/steamworks.dart';
import 'package:ve/UI/Views/create/create_game_view.dart';
import 'package:ve/UI/Views/open/open_game_view.dart';
import 'package:path/path.dart' as path;
import 'package:ve/utils/window_manager.dart'; // 导入窗口管理器
import 'package:ve/main.dart'; // 导入main.dart以使用AppStateContainer
import 'package:provider/provider.dart'; // 导入 provider
import 'package:ve/next-gen-ui/assets.dart'; // 导入 assets
import 'package:ve/next-gen-ui/title_screen/title_screen.dart'; // 导入 TitleScreen

class ProjectListView extends StatefulWidget {
  const ProjectListView({Key? key}) : super(key: key);

  @override
  _ProjectListViewState createState() => _ProjectListViewState();
}

class _ProjectListViewState extends State<ProjectListView> {
  List<String> _projects = [];
  final Map<String, String> _projectImages = {};
  String _selectedProject = '';
  Map<String, bool> _hoverStates = {}; // 添加悬停状态跟踪
  bool _isLoading = false; // 添加加载状态

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  // 添加刷新方法
  Future<void> _refreshProjects() async {
    setState(() {
      _isLoading = true;
    });
    await _loadProjects();
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadProjects() async {
    final savePath = CreateGameView.getSavePath();
    final dir = Directory(savePath);
    
    if (await dir.exists()) {
      final projects = await dir.list().where((entity) {
        // 跳过名为'remote'的文件夹
        final name = path.basename(entity.path);
        return entity is Directory && name.toLowerCase() != 'remote';
      }).map((entity) {
        return path.basename(entity.path);
      }).toList();

      // Load first image for each project
      for (final project in projects) {
        final projectPath = CreateGameView.getSavePath(project);
        final projectDir = Directory(projectPath);
        if (await projectDir.exists()) {
            try {
              final imageFiles = await projectDir.list()
                .where((entity) => entity is File && entity.path.toLowerCase().endsWith('.jpg'))
                .toList();
              
              if (imageFiles.isNotEmpty) {
                final firstImage = imageFiles.first as File;
                _projectImages[project] = firstImage.path;
                print('Found image for $project: ${firstImage.path}');
              } else {
              _projectImages[project] = 'assets/images/logo.png';
                print('No image found for $project, using default');
              }
            } catch (e) {
              print('Error loading image for $project: $e');
              _projectImages[project] = 'assets/images/logo.png';
            }
        }
      }

      setState(() {
        _projects = projects;
        if (projects.isNotEmpty) {
          _selectedProject = projects.first;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: ScreenUtil().screenWidth - 21.width,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).myGames,
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 0),
              Expanded(
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 20,
                    runSpacing: 0,
                    children: _projects.map((project) {
                      final imagePath = _projectImages[project] ?? 'assets/images/logo.png';
                      final isSelected = project == _selectedProject;
                      return _buildProjectCard(context, project, imagePath, isSelected);
                    }).toList(),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectCard(BuildContext context, String title, String imagePath, bool isSelected) {
    // 如果当前项目没有悬停状态，则初始化为false
    if (!_hoverStates.containsKey(title)) {
      _hoverStates[title] = false;
    }
    
    return MouseRegion(
      onEnter: (_) => setState(() => _hoverStates[title] = true),
      onExit: (_) => setState(() => _hoverStates[title] = false),
      child: GestureDetector(
        onTap: () async {
          setState(() {
            _selectedProject = title;
          });
          
          try {
            // 根据设置决定使用新窗口还是当前窗口打开游戏项目
            final projectPath = CreateGameView.getSavePath(title);
            final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForPlaying;
            print('打开游戏项目: $projectPath, 使用新窗口: $useNewWindow');
            
            if (useNewWindow) {
              // 使用WindowManager在新窗口打开
              await WindowManager.openGameInNewWindow(
                projectPath: projectPath,
                projectName: title,
                workshopItemId: '0', // 为本地项目设置默认值
                windowSize: MediaQuery.of(context).size,
              );
            } else {
              // 使用Navigator.push在当前窗口打开，先显示TitleScreen
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => FutureProvider<FragmentPrograms?>(
                    create: (context) => loadFragmentPrograms(),
                    initialData: null,
                    child: TitleScreen(
                      projectPath: projectPath,
                      projectName: title,
                      workshopItemId: '0',
                      showBackButton: true,
                      onStartPressed: () {
                        // 点击开始按钮后导航到OpenGameView
                        Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                            builder: (context) => OpenGameView(
                              projectPath: projectPath,
                              projectName: title,
                              workshopItemId: '0',
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ).then((_) {
                // 当从TitleScreen/OpenGameView返回时，刷新项目列表
                _refreshProjects();
              });
            }
          } catch (e) {
            print('打开游戏项目失败: $e');
            // 显示错误提示
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(S.of(context).openGameError + e.toString())),
              );
            }
          }
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeOutQuad,
          transform: Transform.translate(
            offset: Offset(0, _hoverStates[title]! ? -8.0 : 0.0),
          ).transform,
          child: Stack(
            children: [
              // 使用游戏卡带图片作为背景
              Image.asset(
                'assets/images/game.png',
                width: 175,
                height: 210,
                fit: BoxFit.contain,
              ),
              
              // 添加卡带阴影
              if (_hoverStates[title]!)
                Positioned(
                  bottom: -15,
                  left: 25,
                  right: 25,
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // 顶部标题区域
              Positioned(
                top: 41,
                left: 28,
                right: 28,
                child: Container(
                  height: 22,
                  alignment: Alignment.center,
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              
              // 中间封面区域
              Positioned(
                top: 68.25,
                left: 10.25,
                right: 9.25,
                child: Container(
                  height: 85,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: imagePath.startsWith('assets/')
                    ? Image.asset(
                        imagePath,
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      )
                    : Image.file(
                        File(imagePath),
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 颜色扩展方法
extension ColorExtension on Color {
  Color darker(int percent) {
    assert(1 <= percent && percent <= 100);
    final value = 1 - percent / 100;
    return Color.fromARGB(
      alpha,
      (red * value).round(),
      (green * value).round(),
      (blue * value).round(),
    );
  }

  Color lighter(int percent) {
    assert(1 <= percent && percent <= 100);
    final value = percent / 100;
    return Color.fromARGB(
      alpha,
      (red + ((255 - red) * value)).round(),
      (green + ((255 - green) * value)).round(),
      (blue + ((255 - blue) * value)).round(),
    );
  }
}