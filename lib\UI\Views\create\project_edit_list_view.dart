import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ve/extensions/size_extension.dart';
import 'package:ve/generated/l10n.dart';
import 'dart:io';
import 'package:ve/UI/Views/create/create_game_view.dart';
import 'package:path/path.dart' as path;
import 'package:ve/UI/Views/home/<USER>';
import 'package:ve/UI/Views/home/<USER>';
import 'package:ve/utils/window_manager.dart';
import 'dart:convert';
import 'package:ve/main.dart'; // 导入main.dart以使用AppStateContainer

class ProjectEditListView extends StatefulWidget {
  const ProjectEditListView({Key? key}) : super(key: key);

  @override
  _ProjectEditListViewState createState() => _ProjectEditListViewState();
}

class _ProjectEditListViewState extends State<ProjectEditListView> {
  List<String> _projects = [];
  final Map<String, String> _projectImages = {};
  Map<String, bool> _hoverStates = {}; // 添加悬停状态跟踪
  bool _isLoading = false; // 添加加载状态

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  // 添加刷新方法
  Future<void> _refreshProjects() async {
    setState(() {
      _isLoading = true;
    });
    await _loadProjects();
    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _loadProjects() async {
    final savePath = CreateGameView.getSavePath();
    final dir = Directory(savePath);
    
    if (await dir.exists()) {
      final projects = await dir.list().where((entity) {
        // 跳过名为'remote'的文件夹
        final name = path.basename(entity.path);
        return entity is Directory && name.toLowerCase() != 'remote';
      }).map((entity) {
        return path.basename(entity.path);
      }).toList();

      // 加载每个项目的第一张图片
      for (final project in projects) {
        final projectPath = CreateGameView.getSavePath(project);
        final projectDir = Directory(projectPath);
        if (await projectDir.exists()) {
            try {
              final imageFiles = await projectDir.list()
                .where((entity) => entity is File && entity.path.toLowerCase().endsWith('.jpg'))
                .toList();
              
              if (imageFiles.isNotEmpty) {
                final firstImage = imageFiles.first as File;
                _projectImages[project] = firstImage.path;
                print('找到项目图片: $project: ${firstImage.path}');
              } else {
                _projectImages[project] = 'assets/images/logo.png';
                print('未找到项目图片，使用默认图片');
              }
            } catch (e) {
              print('加载项目图片出错: $project: $e');
              _projectImages[project] = 'assets/images/logo.png';
            }
        }
      }

      setState(() {
        _projects = projects;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        width: ScreenUtil().screenWidth - 21.width,
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                S.of(context).createNewGame,
                style: Theme.of(context).textTheme.headlineMedium,
              ),
              const SizedBox(height: 0),
              Expanded(
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 20,
                    runSpacing: 0,
                    children: [
                      // 添加新建项目按钮
                      _buildCreateProjectCard(context),
                      // 显示现有项目
                      ..._projects.map((project) {
                        final imagePath = _projectImages[project] ?? 'assets/images/logo.png';
                        return _buildProjectCard(context, project, imagePath);
                      }).toList(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 创建新建项目卡片
  Widget _buildCreateProjectCard(BuildContext context) {
    // 为创建项目按钮添加悬停状态
    if (!_hoverStates.containsKey('create_new')) {
      _hoverStates['create_new'] = false;
    }
    
    return MouseRegion(
      onEnter: (_) => setState(() => _hoverStates['create_new'] = true),
      onExit: (_) => setState(() => _hoverStates['create_new'] = false),
      child: GestureDetector(
        onTap: () async {
          try {
            // 先询问项目名称
            final projectName = await showDialog<String>(
              context: context,
              barrierDismissible: false,
              barrierColor: Colors.black.withOpacity(0.5),
              builder: (context) {
                final controller = TextEditingController();
                return PopScope(
                  canPop: false,
                  child: AlertDialog(
                    title: Text(S.of(context).enterProjectName),
                    content: TextField(
                      controller: controller,
                      decoration: InputDecoration(
                        hintText: S.of(context).projectNameHint,
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () {
                          Navigator.pop(context); // 返回上一界面
                        },
                        child: Text(S.of(context).cancel),
                      ),
                      TextButton(
                        onPressed: () async {
                          if (controller.text.isNotEmpty) {
                            // 检查是否使用了保留字'remote'
                            if (controller.text.toLowerCase() == 'remote') {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text(S.of(context).remoteReservedWord)),
                              );
                            } else {
                              // 检查项目文件夹是否已存在
                              final projectPath = CreateGameView.getSavePath(controller.text);
                              final directory = Directory(projectPath);
                              
                              if (await directory.exists()) {
                                // 项目已存在，询问是否进入该项目
                                final shouldEnter = await showDialog<bool>(
                                  context: context,
                                  builder: (context) => AlertDialog(
                                    title: Text(S.of(context).projectExistsTitle),
                                    content: Text(S.of(context).projectExistsContent),
                                    actions: [
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, false),
                                        child: Text(S.of(context).cancel),
                                      ),
                                      TextButton(
                                        onPressed: () => Navigator.pop(context, true),
                                        child: Text(S.of(context).confirm),
                                      ),
                                    ],
                                  ),
                                );
                                
                                if (shouldEnter == true) {
                                  // 用户确认进入项目
                                  Navigator.pop(context, controller.text);
                                }
                              } else {
                                // 项目不存在，正常创建
                                Navigator.pop(context, controller.text);
                              }
                            }
                          }
                        },
                        child: Text(S.of(context).confirm),
                      ),
                    ],
                  ),
                );
              },
            );

            if (projectName != null && projectName.isNotEmpty) {
              // 根据设置决定使用新窗口还是当前窗口打开创建游戏视图
              final projectPath = CreateGameView.getSavePath(projectName);
              final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForEditing;
              print('打开创建游戏视图: $projectPath, 使用新窗口: $useNewWindow');
              
              if (useNewWindow) {
                // 使用WindowManager打开新实例
                await WindowManager.openGameInNewInstance(
                  projectPath: projectPath,
                  projectName: projectName,
                  workshopItemId: '', // 创建新项目时没有创意工坊ID
                  extraArguments: {
                    'mode': 'edit', // 标识这是编辑模式
                    'initialProjectName': projectName, // 传递项目名称
                  },
                );
              } else {
                // 使用Navigator.push在当前窗口打开
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => CreateGameView(
                      initialProjectName: projectName,
                      projectPath: projectPath,
                    ),
                  ),
                ).then((_) {
                  // 当从CreateGameView返回时，刷新项目列表
                  _refreshProjects();
                });
              }
            }
          } catch (e) {
            print('打开创建游戏视图失败: $e');
            // 显示错误提示
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(S.of(context).openGameError + e.toString())),
              );
            }
          }
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeOutQuad,
          transform: Transform.translate(
            offset: Offset(0, _hoverStates['create_new']! ? -8.0 : 0.0),
          ).transform,
          child: Stack(
            children: [
              // 使用游戏卡带图片作为背景
              Image.asset(
                'assets/images/game.png',
                width: 175,
                height: 210,
                fit: BoxFit.contain,
              ),
              
              // 添加卡带阴影
              if (_hoverStates['create_new']!)
                Positioned(
                  bottom: -15,
                  left: 25,
                  right: 25,
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // 顶部标题区域
              Positioned(
                top: 41,
                left: 28,
                right: 28,
                child: Container(
                  height: 22,
                  alignment: Alignment.center,
                  child: Text(
                    S.of(context).createNewGame,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              
              // 中间图标区域
              Positioned(
                top: 68.25,
                left: 10.25,
                right: 9.25,
                child: Container(
                  height: 85,
                  decoration: BoxDecoration(
                    color: Colors.blue,
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Center(
                    child: Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 40,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProjectCard(BuildContext context, String title, String imagePath) {
    // 如果当前项目没有悬停状态，则初始化为false
    if (!_hoverStates.containsKey(title)) {
      _hoverStates[title] = false;
    }
    
    return MouseRegion(
      onEnter: (_) => setState(() => _hoverStates[title] = true),
      onExit: (_) => setState(() => _hoverStates[title] = false),
      child: GestureDetector(
        onTap: () async {
          try {
            // 根据设置决定使用新窗口还是当前窗口打开项目编辑界面
            final projectPath = CreateGameView.getSavePath(title);
            final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForEditing;
            print('打开项目编辑界面: $projectPath, 使用新窗口: $useNewWindow');
            
            if (useNewWindow) {
              // 使用WindowManager打开新实例
              await WindowManager.openGameInNewInstance(
                projectPath: projectPath,
                projectName: title,
                workshopItemId: '', // 如果没有创意工坊ID，传递空字符串
                extraArguments: {
                  'mode': 'edit', // 标识这是编辑模式
                  'initialProjectName': title, // 确保传递初始项目名称参数
                },
              );
            } else {
              // 使用Navigator.push在当前窗口打开
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CreateGameView(
                    initialProjectName: title,
                    projectPath: projectPath,
                  ),
                ),
              ).then((_) {
                // 当从CreateGameView返回时，刷新项目列表
                _refreshProjects();
              });
            }
          } catch (e) {
            print('打开项目编辑界面失败: $e');
            // 显示错误提示
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text(S.of(context).openGameError + e.toString())),
              );
            }
          }
        },
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          curve: Curves.easeOutQuad,
          transform: Transform.translate(
            offset: Offset(0, _hoverStates[title]! ? -8.0 : 0.0),
          ).transform,
          child: Stack(
            children: [
              // 使用游戏卡带图片作为背景
              Image.asset(
                'assets/images/game.png',
                width: 175,
                height: 210,
                fit: BoxFit.contain,
              ),
              
              // 添加卡带阴影
              if (_hoverStates[title]!)
                Positioned(
                  bottom: -15,
                  left: 25,
                  right: 25,
                  child: Container(
                    height: 10,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.25),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: Offset(0, 5),
                        ),
                      ],
                    ),
                  ),
                ),
              
              // 顶部标题区域
              Positioned(
                top: 41,
                left: 28,
                right: 28,
                child: Container(
                  height: 22,
                  alignment: Alignment.center,
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              
              // 中间封面区域
              Positioned(
                top: 68.25,
                left: 10.25,
                right: 9.25,
                child: Container(
                  height: 85,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                  ),
                  clipBehavior: Clip.antiAlias,
                  child: imagePath.startsWith('assets/')
                    ? Image.asset(
                        imagePath,
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      )
                    : Image.file(
                        File(imagePath),
                        fit: BoxFit.contain,
                        width: double.infinity,
                        height: double.infinity,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 颜色扩展方法
extension ColorExtension on Color {
  Color darker(int percent) {
    assert(1 <= percent && percent <= 100);
    final value = 1 - percent / 100;
    return Color.fromARGB(
      alpha,
      (red * value).round(),
      (green * value).round(),
      (blue * value).round(),
    );
  }

  Color lighter(int percent) {
    assert(1 <= percent && percent <= 100);
    final value = percent / 100;
    return Color.fromARGB(
      alpha,
      (red + ((255 - red) * value)).round(),
      (green + ((255 - green) * value)).round(),
      (blue + ((255 - blue) * value)).round(),
    );
  }
}