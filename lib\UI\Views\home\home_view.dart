import 'dart:ui';
import 'dart:io';
import 'dart:math' show max;
import 'dart:convert';
import 'dart:ffi';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:ffi/ffi.dart';
import 'package:intl/intl.dart';
import 'package:ve/utils/data.dart';
import 'package:ve/utils/workshop_path.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:steamworks/steamworks.dart';
import 'package:ve/utils/steam_ex.dart';
import 'package:ve/utils/steam_file_ex.dart';
import 'package:ve/utils/steam_file.dart';
import 'package:ve/utils/steam_cached_image.dart';
import 'package:ve/UI/Views/workshop/workshop_viewmodel.dart';
import 'package:path/path.dart' as path;
import 'package:ve/utils/window_manager.dart'; // 导入窗口管理器
import 'package:ve/main.dart'; // 导入main.dart以使用AppStateContainer
import 'package:provider/provider.dart'; // 导入 provider
import 'package:ve/next-gen-ui/assets.dart'; // 导入 assets
import 'package:ve/next-gen-ui/title_screen/title_screen.dart'; // 导入 TitleScreen
import 'package:ve/utils/fullscreen_manager.dart'; // 导入全屏管理器

import 'package:ve/UI/config/size_config.dart';
import 'package:ve/UI/shared/margins.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_icons_null_safety/flutter_icons_null_safety.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:stacked/stacked.dart';
import 'package:ve/extensions/size_extension.dart';

import 'home_viewmodel.dart';
import 'package:ve/UI/Views/workshop/workshop_view.dart';
import 'package:ve/UI/Views/workshop/workshop_viewmodel.dart';
import 'package:ve/UI/Views/create/create_game_view.dart';
import 'package:ve/UI/Views/create/project_edit_list_view.dart';
import 'package:ve/UI/Views/open/open_game_view.dart';
import 'package:ve/UI/Views/settings/settings_view.dart';
import 'package:ve/generated/l10n.dart';
import 'package:ve/UI/Views/open/project_list_view.dart';
import 'package:ve/UI/Views/workshop/workshop_list_view.dart';
import 'package:ve/utils/coin_manager.dart';

class HomeView extends StatefulWidget {
  static const defaultSavePath = 'saves'; // 简化默认保存路径
  
  const HomeView({super.key});
  
  @override
  State<HomeView> createState() => _HomeViewState();
}

class _HomeViewState extends State<HomeView> {
  // 存储下载完成动画状态的Map
  final Map<String, bool> _downloadCompleteAnimations = {};
  
  // 获取创意工坊物品的详细信息
  Future<Map<String, dynamic>> _getWorkshopItemDetails(String workshopItemId) async {
    try {
      // 创建结果Map
      Map<String, dynamic> result = {
        'title': '',
        'description': '',
        'previewUrl': null
      };
      
      // 创建查询
      final steamUgc = SteamClient.instance.steamUgc;
      final appId = SteamClient.instance.appId;
      
      // 创建查询请求
      final queryHandle = steamUgc.createQueryUgcDetailsRequest(
        using((arena) {
          final itemIds = arena<UnsignedLongLong>();
          itemIds.value = int.parse(workshopItemId);
          return itemIds;
        }), 
        1
      );
      
      if (queryHandle != 0) {
        // 设置查询参数
        steamUgc.setReturnLongDescription(queryHandle, true);
        steamUgc.setReturnMetadata(queryHandle, true);
        steamUgc.setAllowCachedResponse(queryHandle, 0);
        
        // 发送查询请求并等待结果
        final completer = Completer<Map<String, dynamic>>();
        
        SteamClient.instance.registerCallResult<SteamUgcQueryCompleted>(
          asyncCallId: steamUgc.sendQueryUgcRequest(queryHandle),
          cb: (queryResult, failed) {
            if (!failed && queryResult.result == EResult.eResultOK && queryResult.numResultsReturned > 0) {
              // 使用 using 来管理内存
              using((arena) {
                // 获取详细信息
                final detail = arena<SteamUgcDetails>();
                if (steamUgc.getQueryUgcResult(queryResult.handle, 0, detail)) {
                  // 获取标题和描述
                  result['title'] = detail.ref.title.toDartString();
                  result['description'] = detail.ref.description.toDartString();
                  
                  // 获取预览图片URL
                  final previewUrl = arena<Uint8>(256).cast<Utf8>();
                  if (steamUgc.getQueryUgcPreviewUrl(queryResult.handle, 0, previewUrl, 256)) {
                    result['previewUrl'] = previewUrl.toDartString();
                  }
                }
              });
            }
            
            // 释放查询句柄
            steamUgc.releaseQueryUgcRequest(queryHandle);
            completer.complete(result);
          },
        );
        
        return await completer.future;
      }
      
      return result;
    } catch (e) {
      print('获取创意工坊物品详情失败: $e');
      return {
        'title': '',
        'description': '',
        'previewUrl': null
      };
    }
  }

  Future<String> _getValidSavePath() async {
    try {
      return await CreateGameView.getSavePath();
    } catch (e) {
      print('使用默认保存路径: ${HomeView.defaultSavePath}');
      return HomeView.defaultSavePath;
    }
  }

  // 开始定期更新下载进度
  void _startDownloadProgressUpdate(int itemId) {
    print('开始监听下载进度: $itemId');
    // 初始化动画状态
    _downloadCompleteAnimations[itemId.toString()] = false;
    
    // 添加下载完成监听器
    SteamDownloadListener.add(itemId, () {
      print('下载完成触发: $itemId');
      if (mounted) {
        // 触发下载完成动画
        setState(() {
          _downloadCompleteAnimations[itemId.toString()] = true;
        });
        
        // 延迟后重置动画状态，显示已订阅按钮
        Future.delayed(Duration(milliseconds: 800), () {
          if (mounted) {
            setState(() {
              _downloadCompleteAnimations[itemId.toString()] = false;
            });
          }
        });
      }
    }, once: true);
    
    // 启动Steam文件的下载
    try {
      final steamFile = SteamSimpleFile(id: itemId);
      
      // 如果不是订阅状态，先订阅
      if (!steamFile.isSubscribed) {
        SteamClient.instance.subscribe(itemId);
      }
      
      // 启动下载
      SteamClient.instance.steamUgc.downloadItem(itemId, true);
      
      // 定期刷新下载进度，但使用单独的更新逻辑，避免刷新整个UI
      Timer.periodic(Duration(milliseconds: 300), (timer) {
        if (mounted) {
          steamFile.load().then((_) async {
            if (!steamFile.isDownLoading || !mounted) {
              print('下载完成或组件已卸载，停止更新: $itemId');
              timer.cancel();
            } else {
              // 更新下载字节信息
              await steamFile.updateDownloadBytes();
              // 注意：不调用setState()，避免整个UI刷新
              // 下载进度会通过StreamBuilder自动更新
            }
          });
        } else {
          timer.cancel();
        }
      });
    } catch (e) {
      print('启动下载进度更新失败: $e');
    }
  }

  @override
  void initState() {
    super.initState();
  }
  
  @override
  Widget build(BuildContext context) {
    SizeConfig().init(context);
    ScreenUtil.init(context);
    
    // 创建 ScrollController
    ScrollController leftScrollController = ScrollController();
    ScrollController rightScrollController = ScrollController();

    return ViewModelBuilder<HomeViewModel>.reactive(
      builder: (context, model, child) {
        return Scaffold(
          body: Container(
            height: ScreenUtil().screenHeight,
            width: ScreenUtil().screenWidth,
            decoration: const BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/landscape.jpg'),
                fit: BoxFit.cover,
              ),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    buildLeft(context, model, leftScrollController),
                    buildRight(context, model, rightScrollController),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      viewModelBuilder: () => HomeViewModel(),
    );
  }

  Widget buildRight(BuildContext context, HomeViewModel model, ScrollController scrollController) {
    switch (model.currentView) {
      case ViewType.home:
        return buildHomeContent(context, model, scrollController);
      case ViewType.openGame:
        return const ProjectListView();
      case ViewType.workshop:
        final workshopViewModel = WorkshopViewModel();
        return WorkshopListView(model: workshopViewModel);
      case ViewType.createGame:
        return const ProjectEditListView();
      default:
        return buildHomeContent(context, model, scrollController);
    }
  }
  
  Widget buildHomeContent(BuildContext context, HomeViewModel model, ScrollController scrollController) {
    final textTheme = Theme.of(context).textTheme;
    return Material(
      color: Colors.transparent,
      textStyle: TextStyle(
        color: Colors.grey[600],
        fontFamily: 'WorkSans',
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          CupertinoScrollbar(
            controller: scrollController, // 使用传入的 ScrollController
            child: SingleChildScrollView(
              controller: scrollController, // 使用传入的 ScrollController
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 37),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 最近玩过模块
                    FutureBuilder<List<RecentProject>>(
                      future: Future.value(Data.getRecentProjects()),
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                          final recentProjects = snapshot.data!;
                          final displayProjects = recentProjects.take(4).toList();
                          
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                S.of(context).recentlyPlayed,
                                style: textTheme.headlineMedium?.copyWith(
                                  fontFamily: 'WorkSans',
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey[900],
                                ) ?? textTheme.bodyLarge,
                              ),
                              const Ymargin(10),
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    ...displayProjects.map((recent) => Padding(
                                      padding: const EdgeInsets.only(right: 6),
                                      child: SizedBox(
                                        height: 160, //最近游玩和我的项目两种控件总体高度
                                        width: MediaQuery.of(context).size.width * 0.08,
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 150, // 固定高度150，与project_list_view.dart保持一致
                                              width: MediaQuery.of(context).size.width * 0.08,
                                              child: InkWell(
                                                onTap: () async {
                                                  try {
                                                    // 获取是否使用新窗口打开游戏项目的设置
                                                    final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForPlaying;
                                                    
                                                    if (useNewWindow) {
                                                      // 使用新窗口打开
                                                      if (recent.type == 'local') {
                                                        await WindowManager.openGameInNewWindow(
                                                          projectPath: recent.projectPath,
                                                          projectName: recent.projectName,
                                                          workshopItemId: '0',
                                                          windowSize: MediaQuery.of(context).size,
                                                        );
                                                      } else {
                                                        await WindowManager.openGameInNewWindow(
                                                          projectPath: recent.projectPath,
                                                          projectName: recent.projectName,
                                                          workshopItemId: path.basename(recent.projectPath),
                                                          windowSize: MediaQuery.of(context).size,
                                                        );
                                                      }
                                                    } else {
                                                      // 使用当前窗口打开，先显示TitleScreen
                                                      final String workshopId = recent.type == 'local' ? '0' : path.basename(recent.projectPath);
                                                      Navigator.push(
                                                        context,
                                                        MaterialPageRoute(
                                                          builder: (context) => FutureProvider<FragmentPrograms?>(
                                                            create: (context) => loadFragmentPrograms(),
                                                            initialData: null,
                                                            child: TitleScreen(
                                                              projectPath: recent.projectPath,
                                                              projectName: recent.projectName,
                                                              workshopItemId: workshopId,
                                                              showBackButton: true,
                                                              onStartPressed: () {
                                                                // 点击开始按钮后导航到OpenGameView
                                                                Navigator.pushReplacement(
                                                                  context,
                                                                  MaterialPageRoute(
                                                                    builder: (context) => OpenGameView(
                                                                      projectPath: recent.projectPath,
                                                                      projectName: recent.projectName,
                                                                      workshopItemId: workshopId,
                                                                    ),
                                                                  ),
                                                                );
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                      ).then((_) {
                                                        // 当从游戏界面返回时，刷新数据
                                                        model.notifyListeners();
                                                      });
                                                    }
                                                  } catch (e) {
                                                    print('打开游戏项目失败: $e');
                                                    // 显示错误提示
                                                    if (context.mounted) {
                                                      ScaffoldMessenger.of(context).showSnackBar(
                                                        SnackBar(content: Text(S.of(context).openGameError + e.toString())),
                                                      );
                                                    }
                                                  }
                                                },
                                                child: Card(
                                                  semanticContainer: false,
                                                  clipBehavior: Clip.antiAlias,
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius: BorderRadius.circular(8),
                                                  ),
                                                  elevation: 0,
                                                  child: Stack(
                                                    children: [
                                                      recent.type == 'local'
                                                          ? (recent.imagePath != null && recent.imagePath!.isNotEmpty
                                                              ? Image.file(
                                                                  File(recent.imagePath!),
                                                                  fit: BoxFit.cover,
                                                                  width: double.infinity,
                                                                  height: double.infinity,
                                                                  errorBuilder: (context, error, stackTrace) {
                                                                    return Image.asset(
                                                                      'assets/images/logo.png',
                                                                      fit: BoxFit.cover,
                                                                      width: double.infinity,
                                                                      height: double.infinity,
                                                                    );
                                                                  },
                                                                )
                                                              : Image.asset(
                                                                  'assets/images/logo.png',
                                                                  fit: BoxFit.cover,
                                                                ))
                                                          : (recent.imagePath != null && recent.imagePath!.isNotEmpty
                                                              ? Image.file(
                                                                  File(recent.imagePath!),
                                                                  fit: BoxFit.cover,
                                                                  width: double.infinity,
                                                                  height: double.infinity,
                                                                  errorBuilder: (context, error, stackTrace) {
                                                                    return Image.asset(
                                                                      'assets/images/logo.png',
                                                                      fit: BoxFit.cover,
                                                                      width: double.infinity,
                                                                      height: double.infinity,
                                                                    );
                                                                  },
                                                                )
                                                              : Image.asset(
                                                                  'assets/images/logo.png',
                                                                  fit: BoxFit.cover,
                                                                  width: double.infinity,
                                                                  height: double.infinity,
                                                                )),
                                                      // 为创意工坊项目添加已下载图标
                                                      if (recent.type == 'workshop')
                                                        Positioned(
                                                          top: 5,
                                                          right: 5,
                                                          child: GestureDetector(
                                                            onTap: () {
                                                              // 获取工作坊项目文件夹路径
                                                              final workshopPath = recent.projectPath;
                                                              // 打开文件夹
                                                              final uri = Uri.directory(workshopPath);
                                                              launchUrl(uri);
                                                            },
                                                            child: Container(
                                                              padding: const EdgeInsets.all(4),
                                                              decoration: BoxDecoration(
                                                                color: Colors.black.withOpacity(0.6),
                                                                borderRadius: BorderRadius.circular(4),
                                                              ),
                                                              child: const Icon(
                                                                Icons.folder,
                                                                color: Colors.white,
                                                                size: 16,
                                                              ),
                                                            ),
                                                          ),
                                                        ),
                                                      Positioned(
                                                        bottom: 0,
                                                        left: 0,
                                                        right: 0,
                                                        child: Container(
                                                          padding: const EdgeInsets.all(4),
                                                          decoration: BoxDecoration(
                                                            gradient: LinearGradient(
                                                              begin: Alignment.topCenter,
                                                              end: Alignment.bottomCenter,
                                                              colors: [
                                                                Colors.transparent,
                                                                Colors.black.withOpacity(0.7),
                                                              ],
                                                            ),
                                                          ),
                                                          child: Column(
                                                            crossAxisAlignment: CrossAxisAlignment.start,
                                                            children: [
                                                              Text(
                                                                recent.projectName,
                                                                style: const TextStyle(
                                                                  color: Colors.white,
                                                                  fontWeight: FontWeight.bold,
                                                                  fontSize: 12,
                                                                ),
                                                                maxLines: 1,
                                                                overflow: TextOverflow.ellipsis,
                                                              ),
                                                              Text(
                                                                '${DateFormat("yyyy-MM-dd HH:mm").format(recent.lastPlayed)}',
                                                                style: const TextStyle(
                                                                  color: Colors.white70,
                                                                  fontSize: 10,
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            // 删除了图片下方的标题显示
                                          ],
                                        ),
                                      ),
                                    )),
                                    // 添加创建新项目按钮
                                    SizedBox(
                                      height: 160, //最近游玩和我的项目两种控件总体高度
                                      width: MediaQuery.of(context).size.width * 0.08,
                                      child: Column(
                                        children: [
                                          SizedBox(
                                            height: 150, // 固定高度150，与project_list_view.dart保持一致
                                            width: MediaQuery.of(context).size.width * 0.08,
                                            child: Card(
                                              color: Colors.blueAccent[400],
                                              semanticContainer: false,
                                              clipBehavior: Clip.antiAlias,
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              elevation: 0,
                                              child: InkWell(
                                                onTap: () async {
                                                  try {
                                                    // 先询问项目名称
                                                    final projectName = await showDialog<String>(
                                                      context: context,
                                                      barrierDismissible: false,
                                                      barrierColor: Colors.black.withOpacity(0.5),
                                                      builder: (context) {
                                                        final controller = TextEditingController();
                                                        return PopScope(
                                                          canPop: false,
                                                          child: AlertDialog(
                                                            title: Text(S.of(context).enterProjectName),
                                                            content: TextField(
                                                              controller: controller,
                                                              decoration: InputDecoration(
                                                                hintText: S.of(context).projectNameHint,
                                                              ),
                                                            ),
                                                            actions: [
                                                              TextButton(
                                                                onPressed: () {
                                                                  Navigator.pop(context); // 返回上一界面
                                                                },
                                                                child: Text(S.of(context).cancel),
                                                              ),
                                                              TextButton(
                                                                onPressed: () async {
                                                                  if (controller.text.isNotEmpty) {
                                                                    // 检查是否使用了保留字'remote'
                                                                    if (controller.text.toLowerCase() == 'remote') {
                                                                      ScaffoldMessenger.of(context).showSnackBar(
                                                                        SnackBar(content: Text(S.of(context).remoteReservedWord)),
                                                                      );
                                                                    } else {
                                                                      // 检查项目文件夹是否已存在
                                                                      final projectPath = CreateGameView.getSavePath(controller.text);
                                                                      final directory = Directory(projectPath);
                                                                      
                                                                      if (await directory.exists()) {
                                                                        // 项目已存在，询问是否进入该项目
                                                                        final shouldEnter = await showDialog<bool>(
                                                                          context: context,
                                                                          builder: (context) => AlertDialog(
                                                                            title: Text(S.of(context).projectExistsTitle),
                                                                            content: Text(S.of(context).projectExistsContent),
                                                                            actions: [
                                                                              TextButton(
                                                                                onPressed: () => Navigator.pop(context, false),
                                                                                child: Text(S.of(context).cancel),
                                                                              ),
                                                                              TextButton(
                                                                                onPressed: () => Navigator.pop(context, true),
                                                                                child: Text(S.of(context).confirm),
                                                                              ),
                                                                            ],
                                                                          ),
                                                                        );
                                                                        
                                                                        if (shouldEnter == true) {
                                                                          // 用户确认进入项目
                                                                          Navigator.pop(context, controller.text);
                                                                        }
                                                                      } else {
                                                                        // 项目不存在，正常创建
                                                                        Navigator.pop(context, controller.text);
                                                                      }
                                                                    }
                                                                  }
                                                                },
                                                                child: Text(S.of(context).confirm),
                                                              ),
                                                            ],
                                                          ),
                                                        );
                                                      },
                                                    );

                                                    if (projectName != null && projectName.isNotEmpty) {
                                                      // 根据设置决定使用新窗口还是当前窗口打开创建游戏视图
                                                      final projectPath = CreateGameView.getSavePath(projectName);
                                                      final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForEditing;
                                                      print('打开创建游戏视图: $projectPath, 使用新窗口: $useNewWindow');
                                                      
                                                      if (useNewWindow) {
                                                        // 使用WindowManager打开新实例
                                                        await WindowManager.openGameInNewInstance(
                                                          projectPath: projectPath,
                                                          projectName: projectName,
                                                          workshopItemId: '', // 创建新项目时没有创意工坊ID
                                                          extraArguments: {
                                                            'mode': 'edit', // 标识这是编辑模式
                                                            'initialProjectName': projectName, // 传递项目名称
                                                          },
                                                        );
                                                      } else {
                                                        // 使用Navigator.push在当前窗口打开
                                                        Navigator.push(
                                                          context,
                                                          MaterialPageRoute(
                                                            builder: (context) => CreateGameView(
                                                              initialProjectName: projectName,
                                                              projectPath: projectPath,
                                                            ),
                                                          ),
                                                        ).then((_) {
                                                          // 当从CreateGameView返回时，刷新数据
                                                          model.notifyListeners();
                                                        });
                                                      }
                                                    }
                                                  } catch (e) {
                                                    print('打开创建游戏视图失败: $e');
                                                    // 显示错误提示
                                                    if (context.mounted) {
                                                      ScaffoldMessenger.of(context).showSnackBar(
                                                        SnackBar(content: Text(S.of(context).openGameError + e.toString())),
                                                      );
                                                    }
                                                  }
                                                },
                                                child: const Center(
                                                  child: Icon(
                                                    Icons.add,
                                                    color: Colors.white,
                                                    size: 40,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                          const SizedBox(height: 2), // 与其他卡片间距一致
                                          // 删除了显示"创建新游戏"的Container
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Ymargin(20),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                    
                    // 创意工坊推荐项目栏目
                    Text(
                      S.of(context).workshopItems,
                      style: textTheme.headlineMedium?.copyWith(
                        fontFamily: 'WorkSans',
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[900],
                      )?? textTheme.bodyLarge,
                    ),
                    const Ymargin(10),
                    buildWorkshopRecommendCard(context),
                    const Ymargin(25),
                    
                    // 最近编辑项目栏目
                    Text(
                      S.of(context).recentlyEdited,
                      style: textTheme.headlineMedium?.copyWith(
                        fontFamily: 'WorkSans',
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[900],
                      )?? textTheme.bodyLarge,
                    ),
                    const Ymargin(10),
                    FutureBuilder<List<RecentEditedProject>>(
                      future: Future.value(Data.getRecentEditedProjects()),
                      builder: (context, snapshot) {
                        if (snapshot.connectionState == ConnectionState.waiting) {
                          return const CircularProgressIndicator();
                        }
                        
                        if (snapshot.hasData && snapshot.data!.isNotEmpty) {
                          final recentEditedProjects = snapshot.data!;
                          final displayProjects = recentEditedProjects.take(4).toList();
                          
                          return SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: Row(
                              children: [
                                ...displayProjects.map((project) => Padding(
                                  padding: const EdgeInsets.only(right: 6),
                                  child: SizedBox(
                                    height: 160, //最近游玩和我的项目两种控件总体高度
                                    width: MediaQuery.of(context).size.width * 0.08,
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                          height: 150, // 固定高度150，与project_list_view.dart保持一致
                                          width: MediaQuery.of(context).size.width * 0.08,
                                          child: InkWell(
                                            onTap: () async {
                                              try {
                                                // 获取是否使用新窗口打开编辑项目的设置
                                                final useNewWindow = AppStateContainer.of(context, throwOnError: false).useNewWindowForEditing;
                                                
                                                if (useNewWindow) {
                                                  // 使用新窗口打开
                                                  await WindowManager.openGameInNewInstance(
                                                    projectPath: project.projectPath,
                                                    projectName: project.projectName,
                                                    workshopItemId: '', // 编辑模式不需要创意工坊ID
                                                    extraArguments: {
                                                      'mode': 'edit', // 标识这是编辑模式
                                                      'initialProjectName': project.projectName, // 传递项目名称
                                                    },
                                                  );
                                                } else {
                                                  // 使用当前窗口打开
                                                  Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                      builder: (context) => CreateGameView(
                                                        initialProjectName: project.projectName,
                                                        projectPath: project.projectPath,
                                                      ),
                                                    ),
                                                  ).then((_) {
                                                    // 当从CreateGameView返回时，刷新数据
                                                    model.notifyListeners();
                                                  });
                                                }
                                              } catch (e) {
                                                print('打开游戏项目失败: $e');
                                                // 显示错误提示
                                                if (context.mounted) {
                                                  ScaffoldMessenger.of(context).showSnackBar(
                                                    SnackBar(content: Text(S.of(context).openGameError + e.toString())),
                                                  );
                                                }
                                              }
                                            },
                                            child: Card(
                                              semanticContainer: false,
                                              clipBehavior: Clip.antiAlias,
                                              shape: RoundedRectangleBorder(
                                                borderRadius: BorderRadius.circular(8),
                                              ),
                                              elevation: 0,
                                              child: Stack(
                                                children: [
                                                  // 项目图片
                                                  Positioned.fill(
                                                    child: project.imagePath != null && !project.imagePath!.startsWith('assets/')
                                                        ? Image.file(
                                                            File(project.imagePath!),
                                                            fit: BoxFit.cover,
                                                            errorBuilder: (context, error, stackTrace) {
                                                              print('Error loading image: $error');
                                                              return Image.asset(
                                                                'assets/images/logo.png',
                                                                fit: BoxFit.cover,
                                                              );
                                                            },
                                                          )
                                                        : Image.asset(
                                                            'assets/images/logo.png',
                                                            fit: BoxFit.cover,
                                                          ),
                                                  ),
                                                  // 添加渐变阴影和项目信息
                                                  Positioned(
                                                    left: 0,
                                                    right: 0,
                                                    bottom: 0,
                                                    child: Container(
                                                      padding: const EdgeInsets.all(4),
                                                      decoration: BoxDecoration(
                                                        gradient: LinearGradient(
                                                          begin: Alignment.topCenter,
                                                          end: Alignment.bottomCenter,
                                                          colors: [
                                                            Colors.transparent,
                                                            Colors.black.withOpacity(0.7),
                                                          ],
                                                        ),
                                                      ),
                                                      child: Column(
                                                        crossAxisAlignment: CrossAxisAlignment.start,
                                                        children: [
                                                          Text(
                                                            project.projectName,
                                                            style: const TextStyle(
                                                              color: Colors.white,
                                                              fontWeight: FontWeight.bold,
                                                              fontSize: 12,
                                                            ),
                                                            maxLines: 1,
                                                            overflow: TextOverflow.ellipsis,
                                                          ),
                                                          Text(
                                                            '${DateFormat("yyyy-MM-dd HH:mm").format(project.lastEdited)}',
                                                            style: const TextStyle(
                                                              color: Colors.white70,
                                                              fontSize: 10,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                        const SizedBox(height: 2), // 减小间距
                                      ],
                                    ),
                                  ),
                                )),
                                SizedBox(
                                  height: 160, //最近游玩和我的项目两种控件总体高度
                                  width: MediaQuery.of(context).size.width * 0.08,
                                  child: Column(
                                    children: [
                                      SizedBox(
                                        height: 150, // 固定高度150，与project_list_view.dart保持一致
                                        width: MediaQuery.of(context).size.width * 0.08,
                                        child: Card(
                                          color: Colors.blueAccent[400],
                                          semanticContainer: false,
                                          clipBehavior: Clip.antiAlias,
                                          shape: RoundedRectangleBorder(
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          elevation: 0,
                                          child: InkWell(
                                            onTap: () {
                                              Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                  builder: (context) => const CreateGameView()
                                                ),
                                              );
                                            },
                                            child: const Center(
                                              child: Icon(
                                                Icons.add,
                                                color: Colors.white,
                                                size: 40,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(height: 2), // 与其他卡片间距一致
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          );
                        } else {
                          // 如果没有最近编辑的项目，显示创建新项目的按钮
                          return SizedBox(
                            height: 160,
                            width: MediaQuery.of(context).size.width * 0.08,
                            child: Column(
                              children: [
                                SizedBox(
                                  height: 150,
                                  width: MediaQuery.of(context).size.width * 0.08,
                                  child: Card(
                                    color: Colors.blueAccent[400],
                                    semanticContainer: false,
                                    clipBehavior: Clip.antiAlias,
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    elevation: 0,
                                    child: InkWell(
                                      onTap: () {
                                        Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const CreateGameView()
                                          ),
                                        ).then((_) {
                                          // 当从CreateGameView返回时，刷新数据
                                          model.notifyListeners();
                                        });
                                      },
                                      child: const Center(
                                        child: Icon(
                                          Icons.add,
                                          color: Colors.white,
                                          size: 40,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 2),
                              ],
                            ),
                          );
                        }
                      }
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Xmargin(5.width.toDouble()),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                  left: 20,
                  top: MediaQuery.of(context).size.height * 0.04,
                  bottom: 10,
                ),
                child: Text(
                  S.of(context).achievementDisplay,
                  style: textTheme.headlineMedium?.copyWith(
                    fontFamily: 'WorkSans',
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[900],
                  )?? textTheme.bodyLarge,
                ),
              ),
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(
                    bottom: MediaQuery.of(context).size.height * 0.02,
                  ),
                  padding: EdgeInsets.symmetric(
                    vertical: MediaQuery.of(context).size.height * 0.02,
                    horizontal: 20,
                  ),
                  constraints: BoxConstraints(
                    maxWidth: max(300, MediaQuery.of(context).size.width * 0.25),
                    minWidth: 300,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white70,
                    borderRadius: BorderRadius.circular(13),
                  ),
                  child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        FutureBuilder<SteamFile?>(
                          future: Future.value(null).catchError((error) {
                            // 捕获并处理错误，返回null
                            print('获取最受欢迎项目时出错: $error');
                            return null;
                          }).then((_) {
                            // 尝试获取最受欢迎的项目，如果出错则返回null
                            try {
                              return SteamClient.instance.getMostLikedPublishedItem().catchError((error) {
                                print('获取最受欢迎项目时出错: $error');
                                return null;
                              });
                            } catch (e) {
                              print('获取最受欢迎项目时出错: $e');
                              return null;
                            }
                          }),
                          builder: (context, snapshot) {
                            // 默认图片和文本
                            String imagePath = 'assets/images/astronauts.jpg';
                            String projectTitle = S.of(context).uploadNow;
                            int votePercent = 0;
                            
                            // 如果有数据且不为空，使用获取到的项目
                            if (snapshot.connectionState == ConnectionState.done &&
                                snapshot.hasData &&
                                snapshot.data != null) {
                              try {
                                final topItem = snapshot.data!;
                                // 使用项目的预览图
                                if (topItem.cover.isNotEmpty) {
                                  imagePath = topItem.cover;
                                }
                                // 使用项目标题
                                if (topItem.name.isNotEmpty) {
                                  projectTitle = topItem.name;
                                }
                                // 计算点赞百分比（使用voteUp值，最多100%）
                                votePercent = (topItem.voteUp > 100) ? 100 : topItem.voteUp;
                              } catch (e) {
                                print('处理最受欢迎项目数据时出错: $e');
                                // 使用默认值
                              }
                            }
                            
                            return Column(children: [
                              Container(
                                height: MediaQuery.of(context).size.height * 0.5,
                                width: double.infinity,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(11),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(11),
                                  child: imagePath.startsWith('assets/') 
                                    ? Image.asset(
                                        imagePath,
                                        fit: BoxFit.cover,
                                        width: double.infinity,
                                        height: double.infinity,
                                      )
                                    : imagePath.startsWith('http') 
                                      ? SteamCachedImage(imagePath, fit: BoxFit.cover)
                                      : Image.file(
                                          File(imagePath),
                                          fit: BoxFit.cover,
                                          width: double.infinity,
                                          height: double.infinity,
                                          errorBuilder: (context, error, stackTrace) {
                                            print('Error loading image: $error');
                                            return Image.asset(
                                              'assets/images/astronauts.jpg',
                                              fit: BoxFit.cover,
                                              width: double.infinity,
                                              height: double.infinity,
                                            );
                                          },
                                        ),
                                ),
                              ),
                              const Ymargin(18),
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      projectTitle,
                                      style: textTheme.titleMedium?.copyWith(
                                        fontFamily: 'WorkSans',
                                        fontWeight: FontWeight.bold,
                                        color: Colors.grey[900],
                                      ) ?? textTheme.bodyLarge,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                  const SizedBox(width: 10),
                                  Text(
                                    '$votePercent%',
                                    style: textTheme.titleSmall?.copyWith(
                                      fontFamily: 'WorkSans',
                                      fontWeight: FontWeight.normal,
                                      color: Colors.grey[900],
                                    ) ?? textTheme.bodyMedium,
                                  ),
                                ],
                              ),
                              const Ymargin(14),
                              LinearPercentIndicator(
                                width: MediaQuery.of(context).size.width * 0.2,
                                padding: const EdgeInsets.symmetric(horizontal: 10),
                                lineHeight: 7.0,
                                percent: votePercent / 100,
                                backgroundColor: Colors.grey[350],
                                progressColor: Colors.blueAccent[400],
                              ),
                            ]);
                          },
                        ),
                        const Ymargin(20),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 6,
                            bottom: 20,
                          ),
                          child: Container(
                            height: 35,
                            width: 110,
                            decoration: BoxDecoration(
                              color: Colors.blueAccent[400],
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Center(
                              child: Text(
                                'Play',
                                style: textTheme.titleMedium?.copyWith(
                                  fontFamily: 'WorkSans',
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                )?? textTheme.bodyLarge, // 如果 titleMedium 为 null，则回退到 bodyLarge
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 6,
                            bottom: 12,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                FontAwesome.clock_o,
                                color: Colors.grey[600],
                                size: 17,
                              ),
                              const Xmargin(10),
                              const Text(
                                "You've played 48 hours",
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 6,
                            bottom: 20,
                          ),
                          child: Row(
                            children: [
                              Icon(
                                FontAwesome.calendar_o,
                                color: Colors.grey[600],
                                size: 15,
                              ),
                              const Xmargin(10),
                              const Text(
                                "Last played 06.07.2020",
                                style: TextStyle(
                                  fontWeight: FontWeight.w400,
                                  color: Colors.black,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          width: 300,
                          height: 100,
                          padding: const EdgeInsets.all(15),
                          decoration: BoxDecoration(
                            color: Colors.grey[350],
                            borderRadius: BorderRadius.circular(11),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                S.of(context).achievements,
                                style: TextStyle(
                                  color: Colors.black,
                                ),
                              ),
                              const Ymargin(10),
                              Row(
                                children: [
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: const DecorationImage(
                                        image:
                                            AssetImage('assets/images/corgi.jpg'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const Xmargin(8),
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: const DecorationImage(
                                        image:
                                            AssetImage('assets/images/giraffe.jpg'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const Xmargin(8),
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: const DecorationImage(
                                        image:
                                            AssetImage('assets/images/lake.jpg'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const Xmargin(8),
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: const DecorationImage(
                                        image:
                                            AssetImage('assets/images/dent-blanche.jpg'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                  ),
                                  const Xmargin(8),
                                  Container(
                                    height: 40,
                                    width: 40,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(8),
                                      image: const DecorationImage(
                                        image:
                                            AssetImage('assets/images/women.jpg'),
                                        fit: BoxFit.cover,
                                      ),
                                    ),
                                    child: DecoratedBox(
                                      decoration: BoxDecoration(
                                        color: Colors.grey[800]?.withOpacity(0.6),
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: const Center(
                                        child: Text(
                                          '+58',
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        const Ymargin(20),
                        Text(
                          S.of(context).screenshots,
                          style: TextStyle(
                            color: Colors.black,
                          ),
                        ),
                        const Ymargin(10),
                        SizedBox(
                          height: 200,
                          width: 300,
                          child: GridView.count(
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            crossAxisCount: 3,
                            children: [
                              Container(
                                height: 40,
                                width: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: const DecorationImage(
                                    image: AssetImage(
                                        'assets/images/flower.jpg'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Container(
                                height: 40,
                                width: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: const DecorationImage(
                                    image: AssetImage('assets/images/giraffe.jpg'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Container(
                                height: 40,
                                width: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: const DecorationImage(
                                    image: AssetImage('assets/images/women.jpg'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Container(
                                height: 40,
                                width: 40,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: const DecorationImage(
                                    image: AssetImage(
                                        'assets/images/wolf.jpg'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  image: const DecorationImage(
                                    image: AssetImage('assets/images/lake.jpg'),
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                child: DecoratedBox(
                                  decoration: BoxDecoration(
                                    color: Colors.grey[800]?.withOpacity(0.8),
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: const Center(
                                    child: Text(
                                      '+12',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 12,
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 构建创意工坊推荐卡片
  Widget buildWorkshopRecommendCard(BuildContext context) {
    // 创意工坊项目ID
    const String workshopItemId = '3480276727';
    
    // 初始化变量 - 使用本地化的标题和描述
    String title = S.of(context).workshopRecommendedTitle;
    String description = S.of(context).workshopRecommendedDescription;
    
    // 获取物品ID
    final itemId = int.parse(workshopItemId);
    
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.35,
      width: MediaQuery.of(context).size.width * 0.42,
      child: Card(
        semanticContainer: false,
        clipBehavior: Clip.antiAlias,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(11),
        ),
        elevation: 0,
        child: InkWell(
          onTap: () async {
            try {
              // 获取工作坊项目路径
              final path = WorkshopPath.getShopPath(workshopItemId);
              final directory = Directory(path);
              
              // 检查是否已订阅并下载
              final isSubscribed = await SteamClient.instance.isSubscribed(int.parse(workshopItemId));
              
              if (!isSubscribed) {
                // 显示未订阅提示弹窗
                showDialog(
                  context: context, 
                  builder: (context) => AlertDialog(
                    title: Text(S.of(context).Unsubscribed),
                    content: Text(S.of(context).itemNotSubscribed),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text(S.of(context).cancel),
                      ),
                      ElevatedButton(
                        onPressed: () async {
                          Navigator.pop(context);
                          try {
                            await SteamClient.instance.subscribe(int.parse(workshopItemId));
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text(S.of(context).subscribeSuccess)),
                            );
                          } catch (e) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(content: Text(S.of(context).subscribeFailed + e.toString())),
                            );
                          }
                        },
                        child: Text(S.of(context).Subscribe),
                      ),
                    ],
                  ),
                );
                return;
              }
              
              // 检查物品是否正在下载中
              final itemId = int.parse(workshopItemId);
              final steamFile = SteamSimpleFile(id: itemId);
              await steamFile.load();
              
              if (steamFile.isDownLoading) {
                // 如果正在下载中，显示提示信息并启动下载进度更新
                _startDownloadProgressUpdate(itemId);
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).downloadingPleaseWait)),
                );
                return;
              }
              
              // 检查目录是否存在且不为空
              if (!await directory.exists() || (await directory.list().isEmpty)) {
                // 如果目录不存在或为空，说明下载可能刚完成但文件尚未解压完成
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).filePreparingPleaseRetry)),
                );
                return;
              }
              
              // 已订阅且下载完成，在新窗口中打开
              // 记录路径信息，但不打印敏感信息
              print('准备打开创意工坊项目');
              await WindowManager.openGameInNewWindow(
                projectPath: path,
                projectName: S.of(context).workshopRecommendedTitle,
                workshopItemId: workshopItemId,
                windowSize: MediaQuery.of(context).size,
              );
            } catch (e) {
              print('打开游戏项目失败: $e');
              // 显示错误提示
              if (context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).openGameError + e.toString())),
                );
              }
            }
          },
          child: Row(
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.2,
                decoration: BoxDecoration(
                  image: const DecorationImage(
                      image: AssetImage('assets/images/market.jpg'),
                      fit: BoxFit.cover),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 14.0,
                  vertical: 25,
                ),
                child: SizedBox(
                  height: 180,
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment:
                          CrossAxisAlignment.start,
                      children: [
                        const Ymargin(20),
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontFamily: 'WorkSans',
                            fontWeight: FontWeight.bold,
                            color: Colors.grey[800],
                          ) ?? Theme.of(context).textTheme.bodyLarge,
                        ),
                        const Ymargin(8),
                        SizedBox(
                          width: 185,
                          child: Text(
                            description,
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.grey,
                              fontFamily: 'WorkSans',
                            )?? Theme.of(context).textTheme.bodyLarge,
                          ),
                        ),
                        const Ymargin(14),
                        // 构建订阅按钮或下载进度条
                        StreamBuilder<void>(
                          // 创建一个周期性的流来自动刷新订阅状态和下载进度
                          stream: Stream.periodic(Duration(milliseconds: 500)),
                          builder: (context, _) {
                            return FutureBuilder<bool>(
                              future: SteamClient.instance.isSubscribed(itemId),
                              builder: (context, subscriptionSnapshot) {
                                // 使用SteamSimpleFile检查真实的订阅状态
                                bool isSubscribed = false;
                                if (subscriptionSnapshot.hasData) {
                                  isSubscribed = subscriptionSnapshot.data!;
                                  // 双重检查订阅状态
                                  final steamFile = SteamSimpleFile(id: itemId);
                                  isSubscribed = isSubscribed && steamFile.isSubscribed;
                                }
                                
                                // 检查是否正在下载中
                                // 先检查是否有活跃的下载任务
                                final isDownloading = SteamDownloadListener.isDownloading(itemId);
                                final progress = SteamDownloadListener.getProgress(itemId);
                                
                                // 如果正在下载中，显示下载进度
                                if (isDownloading) {
                                  return AnimatedContainer(
                                    duration: Duration(milliseconds: 300),
                                    width: 180,
                                    height: 36,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(18),
                                      gradient: LinearGradient(
                                        colors: [Colors.blue[700]!, Colors.blue[500]!],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.blue.withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Stack(
                                      children: [
                                        // 进度条背景
                                        ClipRRect(
                                          borderRadius: BorderRadius.circular(18),
                                          child: LinearProgressIndicator(
                                            value: progress,
                                            backgroundColor: Colors.transparent,
                                            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue[300]!),
                                            minHeight: 36,
                                          ),
                                        ),
                                        // 文字和图标
                                        Center(
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.center,
                                            children: [
                                              SizedBox(width: 8),
                                              SizedBox(
                                                width: 16,
                                                height: 16,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                                ),
                                              ),
                                              SizedBox(width: 8),
                                              Text(
                                                '${S.of(context).downloading} ${(progress * 100).toStringAsFixed(0)}%',
                                                style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.bold,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  );
                                } 
                                // 如果已订阅，显示已订阅按钮
                                else if (isSubscribed) {
                                  return AnimatedContainer(
                                    duration: Duration(milliseconds: 300),
                                    width: 180,
                                    height: 36,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(18),
                                      gradient: LinearGradient(
                                        colors: [Colors.green[600]!, Colors.green[400]!],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.green.withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(18),
                                        onTap: () {
                                          // 保留按钮但移除功能
                                          ScaffoldMessenger.of(context).showSnackBar(
                                            SnackBar(content: Text(S.of(context).alreadySubscribed)),
                                          );
                                        },
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.check, size: 16, color: Colors.white),
                                            SizedBox(width: 6),
                                            Text(
                                              S.of(context).Subscribed,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                } 
                                // 如果未订阅，显示订阅按钮
                                else {
                                  return AnimatedContainer(
                                    duration: Duration(milliseconds: 300),
                                    width: 180,
                                    height: 36,
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(18),
                                      gradient: LinearGradient(
                                        colors: [Theme.of(context).primaryColor, Colors.blue[400]!],
                                        begin: Alignment.centerLeft,
                                        end: Alignment.centerRight,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: Theme.of(context).primaryColor.withOpacity(0.3),
                                          blurRadius: 8,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius: BorderRadius.circular(18),
                                        onTap: () async {
                                          try {
                                            // 订阅项目
                                            await SteamClient.instance.subscribe(itemId);
                                            // 下载项目
                                            SteamClient.instance.steamUgc.downloadItem(itemId, true);
                                            // 启动下载进度更新
                                            _startDownloadProgressUpdate(itemId);
                                            // 显示订阅成功消息
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(content: Text(S.of(context).subscribeSuccess)),
                                            );
                                          } catch (e) {
                                            ScaffoldMessenger.of(context).showSnackBar(
                                              SnackBar(content: Text(S.of(context).subscribeFailed + e.toString())),
                                            );
                                          }
                                        },
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(Icons.add, size: 16, color: Colors.white),
                                            SizedBox(width: 6),
                                            Text(
                                              S.of(context).Subscribe,
                                              style: TextStyle(
                                                fontSize: 12,
                                                fontWeight: FontWeight.bold,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                }
                              },
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildNewsCard(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;

    return Stack(
      children: [
        Container(
          height: MediaQuery.of(context).size.height * 0.5,
          width: MediaQuery.of(context).size.width * 0.42,
          decoration: BoxDecoration(
            image: const DecorationImage(
              image: AssetImage('assets/images/hot-air-balloon.jpg'),
              fit: BoxFit.cover,
            ),
            borderRadius: BorderRadius.circular(11),
          ),
        ),
        Positioned(
          height: 170,
          width: 567,
          bottom: 0,
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(11),
              bottomRight: Radius.circular(11),
            ),
            child: BackdropFilter(
              filter: ImageFilter.blur(
                sigmaX: 16,
                sigmaY: 16,
              ),
              child: Container(
                color: Colors.grey[600]?.withOpacity(0.5),
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 60,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 26,
              vertical: 12,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'HARLEY QUINN ARRIVES IN FORTNITE',
                  style: textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontFamily: 'WorkSans',
                    fontWeight: FontWeight.w600,
                  )?? textTheme.bodyLarge,
                ),
                const Ymargin(4),
                SizedBox(
                  width: 500,
                  child: Text(
                    "The Item Shop features the Harley Quinn Bundle, which includes the Harley Quinn Outfit and Harley Hitter and Punchline Pickaxes. If you're up for hijinx, Harley arrives with new Challenges that will transform her from Lil Monster XoXo Harley to Always Fantabulous Harley.",
                    style: textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontFamily: 'WorkSans',
                    ) ?? const TextStyle(
                      color: Colors.white,
                      fontFamily: 'WorkSans',
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget buildLeft(BuildContext context, HomeViewModel model, ScrollController scrollController) {
    return Material(
      textStyle: const TextStyle(
        color: Colors.white70,
        fontFamily: 'Lato',
      ),
      child: Container(
        width: 21.width.toDouble(),
        height: 100.height.toDouble(),
        decoration: BoxDecoration(
          color: Colors.blueAccent[400],
        ),
        child: CupertinoScrollbar(
          controller: scrollController,
          child: SingleChildScrollView(
            controller: scrollController,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  height: 6.height.toDouble(), // 增加顶部空间
                ),
                CupertinoScrollbar(
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: <Widget>[
                        Padding(
                          padding: EdgeInsets.only(
                            left: 3.0.width.toDouble(), // 增加左侧间距
                            right: 1.8.width.toDouble(), // 增加右侧间距
                          ),
                          child: const CircleAvatar(
                            radius: 22, // 增加头像大小
                            backgroundImage:
                                AssetImage('assets/images/logo.png'),
                          ),
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text(
                              'VE',
                              style: TextStyle(
                                fontSize: 20, // 增加字体大小
                                color: Colors.white,
                                fontWeight: FontWeight.bold, // 加粗显示
                              ),
                            ),
                            const SizedBox(height: 6), // 增加垂直间距
                            const Opacity(
                              opacity: 0.7,
                              child: Text(
                                'Beta 0.1.3',
                                style: TextStyle(
                                  fontSize: 15, // 增加字体大小
                                ),
                              ),
                            ),
                            const SizedBox(height: 12), // 增加垂直间距
                            StreamBuilder<int>(
                              stream: CoinManager().coinStream,
                              initialData: 0,
                              builder: (context, snapshot) {
                                return Row(
                                  children: [
                                    const Icon(
                                      Icons.monetization_on,
                                      color: Colors.amber,
                                      size: 18, // 增加图标大小
                                    ),
                                    const SizedBox(width: 8), // 增加水平间距
                                    Text(
                                      '${snapshot.data ?? 0}',
                                      style: const TextStyle(
                                        fontSize: 16, // 增加字体大小
                                        color: Colors.amber,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Ymargin(5.0.height.toDouble()), // 增加垂直间距
                Padding(
                  padding: EdgeInsets.only(
                    left: 3.5.width.toDouble(), // 增加左侧间距
                    bottom: 3.0.height.toDouble(), // 增加底部间距
                  ),
                  child: Text(
                    S.of(context).games,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: MediaQuery.of(context).size.width * 0.012, // 增加字体大小
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 3.5.width.toDouble()), // 增加左侧间距
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () => model.changeView(ViewType.home),
                        child: iconList1(
                          iconData: Icons.home,
                          title: S.of(context).home,
                          active: model.currentView == ViewType.home,
                        ),
                      ),
                      SizedBox(height: 1.0.height.toDouble()), // 增加按钮之间的间距
                      InkWell(
                        onTap: () => model.changeView(ViewType.createGame),
                        child: iconList1(
                          iconData: Icons.create,
                          title: S.of(context).createNewGame,
                          active: model.currentView == ViewType.createGame,
                        ),
                      ),
                      SizedBox(height: 1.0.height.toDouble()), // 增加按钮之间的间距
                      InkWell(
                        onTap: () => model.changeView(ViewType.workshop),
                        child: iconList1(
                          iconData: Icons.work,
                          title: S.of(context).workshop,
                          active: model.currentView == ViewType.workshop,
                        ),
                      ),
                      SizedBox(height: 1.0.height.toDouble()), // 增加按钮之间的间距
                      InkWell(
                        onTap: () => model.changeView(ViewType.openGame),
                        child: iconList1(
                          iconData: Icons.play_arrow,
                          title: S.of(context).openGame,
                          active: model.currentView == ViewType.openGame,
                        ),
                      ),
                    ],
                  ),
                ),
                Ymargin(5.0.height.toDouble()), // 增加模块之间的间距
                Padding(
                  padding: EdgeInsets.only(
                    left: 3.5.width.toDouble(), // 增加左侧间距
                    bottom: 3.0.height.toDouble(), // 增加底部间距
                  ),
                  child: Text(
                    S.of(context).settings,
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontSize: MediaQuery.of(context).size.width * 0.012, // 增加字体大小
                    ),
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 3.5.width.toDouble()), // 增加左侧间距
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => const SettingsView()),
                          );
                        },
                        child: iconList2(
                          iconData: Icons.settings,
                          title: S.of(context).allSettings,
                          active: model.secondIndex == 1,
                        ),
                      ),
                      SizedBox(height: 1.0.height.toDouble()), // 增加按钮之间的间距
                      InkWell(
                        onTap: () {
                          // 切换全屏状态
                          try {
                            final app = AppStateContainer.of(context, throwOnError: false);
                            app.toggleFullScreen();
                          } catch (e) {
                            print('切换全屏状态失败: $e');
                          }
                        },
                        child: iconList2(
                          iconData: Icons.fullscreen,
                          title: S.of(context).fullscreen,
                          active: model.secondIndex == 2,
                        ),
                      ),
                      SizedBox(height: 1.0.height.toDouble()), // 增加按钮之间的间距
                      InkWell(
                        onTap: () {
                          // 退出应用
                          showDialog(
                            context: context,
                            builder: (context) => AlertDialog(
                              title: Text(S.of(context).exit),
                              content: Text(S.of(context).confirmExitApp),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: Text(S.of(context).cancel),
                                ),
                                TextButton(
                                  onPressed: () {
                                    // 退出应用
                                    exit(0);
                                  },
                                  child: Text(S.of(context).exit),
                                ),
                              ],
                            ),
                          );
                        },
                        child: iconList2(
                          iconData: Icons.exit_to_app,
                          title: S.of(context).exit,
                          active: model.secondIndex == 3,
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 3.0.height.toDouble()), // 增加底部间距
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget iconList1({
    required String title,
    required IconData iconData,
    bool active = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.2.height.toDouble()),
      child: AnimatedOpacity(
        opacity: active ? 1 : 0.5,
        duration: const Duration(milliseconds: 300),
        child: SizedBox(
          width: 20.width.toDouble(),
          child: Stack(
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: Colors.white,
                    size: 1.6.width.toDouble(),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 2.0.width.toDouble()),
                    child: Text(
                      title,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: active ? FontWeight.bold : FontWeight.w300,
                        fontSize: 1.5.width.toDouble(),
                      ),
                    ),
                  ),
                ],
              ),
              if (active)
                Positioned(
                  right: 0,
                  child: Icon(
                    Icons.chevron_left,
                    color: Colors.white,
                    size: 1.6.width.toDouble(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget iconList2({
    required String title,
    required IconData iconData,
    bool active = false,
  }) {
    return Padding(
      padding: EdgeInsets.only(bottom: 2.2.height.toDouble()),
      child: AnimatedOpacity(
        opacity: active ? 1 : 0.5,
        duration: const Duration(milliseconds: 300),
        child: SizedBox(
          width: 20.width.toDouble(),
          child: Stack(
            children: [
              Row(
                children: [
                  Icon(
                    iconData,
                    color: Colors.white,
                    size: 1.6.width.toDouble(),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 2.0.width.toDouble()),
                    child: Text(
                      title,
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: active ? FontWeight.bold : FontWeight.w300,
                        fontSize: 1.5.width.toDouble(),
                      ),
                    ),
                  ),
                ],
              ),
              if (active)
                Positioned(
                  right: 0,
                  child: Icon(
                    Octicons.primitive_dot,
                    color: Colors.white,
                    size: 1.4.width.toDouble(),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
