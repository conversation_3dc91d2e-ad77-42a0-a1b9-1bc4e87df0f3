// ignore_for_file: public_member_api_docs

import 'element_settings_menu.dart';
import 'hooks_mobile.dart'
    if (dart.library.js) 'hooks_web.dart';
import 'text_menu.dart';
import 'branch_settings_menu.dart'; // 导入分支设置菜单
import 'global_value_dialog.dart'; // 导入全局数值对话框
import 'global_value_settings_menu.dart'; // 导入选项及数值变化设置菜单
import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:star_menu/star_menu.dart';
import 'dart:io';
import 'dart:async'; // 添加Timer支持
import 'dart:convert'; // 添加JSON编解码支持
import 'package:file_picker/file_picker.dart';
import 'package:shared_preferences/shared_preferences.dart'; // 添加SharedPreferences支持
import '../../../utils/video.dart';
import 'package:media_kit/media_kit.dart'; // Provides [Player], [Media], [Playlist] etc.
import 'package:media_kit_video/media_kit_video.dart';
import 'video_window.dart';
import 'floating_video_window.dart';
import 'package:steamworks/steamworks.dart';
import 'package:ve/utils/steam_ex.dart';
import 'package:ve/generated/l10n.dart';
import 'package:ve/utils/steam_globals.dart';
import 'package:ve/utils/steam_file.dart';
import 'package:ve/utils/steam_file_ex.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:intl/intl.dart';
import 'set_time_menu.dart';
import 'package:path/path.dart' as p;
import 'package:ve/utils/data.dart';
import 'package:ve/utils/asset_path.dart';
import 'package:ve/utils/workshop_path.dart';
import 'package:ve/utils/save_archive.dart';
import 'package:flutter_flow_chart/src/ui/draw_arrow.dart'; // 添加DrawingArrow导入

// 定义全局数值类
class GlobalVariable {
  String name;
  dynamic value;
  String type;

  GlobalVariable({required this.name, required this.value, required this.type});

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'value': value,
      'type': type,
    };
  }

  factory GlobalVariable.fromJson(Map<String, dynamic> json) {
    return GlobalVariable(
      name: json['name'],
      value: json['value'],
      type: json['type'],
    );
  }
}

class CreateGameView extends StatefulWidget {
  final String? initialProjectName;
  final String? projectPath;
  final bool enableSteamFeatures;
  
  const CreateGameView({
    this.initialProjectName, 
    this.projectPath, 
    this.enableSteamFeatures = true,
    super.key
  });

  static String getSavePath([String? projectName]) {
    final currentDir = Directory.current;
    final path = p.join(currentDir.path, 'SavesDir');
    
    // 检查路径是否存在
    final dir = Directory(path);
    if (!dir.existsSync()) {
      print('警告: 项目存放路径不存在: $path');
    }

    return projectName != null ? '$path/$projectName' : path;
  }

  // 保存最近编辑项目的方法
  static void saveRecentEditedProject(String projectPath, String projectName, {String? imagePath}) {
    final recentEditedProject = RecentEditedProject(
      projectPath: projectPath,
      projectName: projectName,
      type: 'local', // 编辑功能目前只支持本地项目
      lastEdited: DateTime.now(),
      imagePath: imagePath,
    );
    
    Data.saveRecentEditedProject(recentEditedProject);
  }

  @override
  _CreateGameViewState createState() => _CreateGameViewState();
}

class _CreateGameViewState extends State<CreateGameView> {
  Dashboard dashboard = Dashboard(projectPath: '', projectName: '');
  late final Player player;
  late final VideoController videoController;
  bool showVideo = false;
  Offset videoWindowPosition = Offset(20, 20); // 添加位置变量
  String? projectName; // 添加项目名变量
  Timer? _autoSaveTimer; // 添加自动保存计时器
  double _autoSaveInterval = 30.0; // 自动保存间隔
  String _autoSaveUnit = 's'; // 自动保存单位
  int _disconnectedVideoCount = 0; // 未连接视频节点数量
  int _startNodeVideoCount = 0; // 起点节点后视频节点数量
  bool _enableFlowchartCheck = true; // 是否启用流程图检测
  bool _isAutoSavePausedByUI = false; // 新增：用于UI操作暂停自动保存的状态
  bool _elementMenuActionWillOpenDialog = false; // 新增：用于判断菜单关闭是否因为要打开对话框
  
  // 添加全局数值列表
  List<GlobalVariable> globalVariables = [];

  // 添加用于安全显示SnackBar的变量
  String? _pendingSnackBarMessage; // 待显示的SnackBar消息
  bool _showPendingSnackBar = false; // 是否显示待显示的SnackBar

  @override
  void initState() {
    super.initState();
    player = Player(
      configuration: const PlayerConfiguration(
        libass: true,
        logLevel: MPVLogLevel.debug,
        vo: 'gpu-next',
      )
    );
    videoController = VideoController(player);

    // 初始化dashboard的项目路径
    if (widget.projectPath?.isNotEmpty == true) {
      dashboard.projectPath = widget.projectPath!;
    }

    // 加载自动保存设置
    _loadAutoSaveSettings();

    // 加载全局数值
    _loadGlobalVariables();

    // 如果提供了初始项目名称，则直接加载项目，否则显示创建项目对话框
    if (widget.initialProjectName != null) {
      projectName = widget.initialProjectName;
      _loadExistingProject();
    } else {
      // 在页面加载完成后显示创建项目对话框
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        projectName = await showDialog<String>(
          context: context,
          barrierDismissible: false,
          barrierColor: Colors.black.withOpacity(0.5),
          builder: (context) {
            final controller = TextEditingController();
            return PopScope(
              canPop: false,
              child: AlertDialog(
                title: Text(S.of(context).enterProjectName),
                content: TextField(
                  controller: controller,
                  decoration: InputDecoration(
                    hintText: S.of(context).projectNameHint,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.pop(context); // 返回上一界面
                    },
                    child: Text(S.of(context).cancel),
                  ),
                  TextButton(
                    onPressed: () async {
                      if (controller.text.isNotEmpty) {
                        // 检查是否使用了保留字'remote'
                        if (controller.text.toLowerCase() == 'remote') {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text(S.of(context).remoteReservedWord)),
                          );
                        } else {
                          // 检查项目文件夹是否已存在
                          final projectPath = CreateGameView.getSavePath(controller.text);
                          final directory = Directory(projectPath);
                          
                          if (await directory.exists()) {
                            // 项目已存在，询问是否进入该项目
                            final shouldEnter = await showDialog<bool>(
                              context: context,
                              builder: (context) => AlertDialog(
                                  title: Text(S.of(context).projectExistsTitle),
                                  content: Text(S.of(context).projectExistsContent),
                                actions: [
                                  TextButton(
                                    onPressed: () => Navigator.pop(context, false),
                                    child: Text(S.of(context).cancel),
                                  ),
                                  TextButton(
                                    onPressed: () => Navigator.pop(context, true),
                                    child: Text(S.of(context).confirm),
                                  ),
                                ],
                              ),
                            );
                            
                            if (shouldEnter == true) {
                              // 用户确认进入项目
                              Navigator.pop(context, controller.text);
                            }
                          } else {
                            // 项目不存在，正常创建
                            Navigator.pop(context, controller.text);
                          }
                        }
                      }
                    },
                    child: Text(S.of(context).confirm),
                  ),
                ],
              ),
            );
          },
        );

        if (projectName != null && projectName!.isNotEmpty) {
          final savePath = CreateGameView.getSavePath(projectName);
          final directory = Directory(savePath);
          if (!await directory.exists()) {
            await directory.create(recursive: true);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('${S.of(context).projectCreated}: $savePath')),
            );
          }
          dashboard.projectPath = savePath;
          dashboard.projectName = projectName!;
          
          // 如果项目文件夹已存在，加载项目
          if (await directory.exists()) {
            _loadExistingProject();
          }
          
          // 设置自动保存
          _startAutoSave();
        } else {
          // 用户取消了创建项目，返回上一页面
          Navigator.of(context).pop();
        }
      });
    }

    // 记录本次打开的项目为最近编辑项目
    if (widget.projectPath != null && widget.initialProjectName != null) {
      CreateGameView.saveRecentEditedProject(widget.projectPath!, widget.initialProjectName!);
    }
  }

  // 加载自动保存设置
  Future<void> _loadAutoSaveSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      setState(() {
        _autoSaveInterval = prefs.getDouble('autoSaveInterval') ?? 30.0;
        _autoSaveUnit = prefs.getString('autoSaveUnit') ?? 's';
        // 确保值在有效范围内
        if (_autoSaveUnit == 's') {
          if (_autoSaveInterval < 10) {
            _autoSaveInterval = 10.0;
          } else if (_autoSaveInterval > 69) {
            _autoSaveInterval = 69.0;
          }
        } else {
          if (_autoSaveInterval < 1) {
            _autoSaveInterval = 1.0;
          } else if (_autoSaveInterval > 60) {
            _autoSaveInterval = 60.0;
          }
        }
        _enableFlowchartCheck = prefs.getBool('enableFlowchartCheck') ?? true;
      });
      debugPrint('加载自动保存设置: $_autoSaveInterval $_autoSaveUnit');
      debugPrint('流程图检测: ${_enableFlowchartCheck ? "启用" : "禁用"}');
    } catch (e) {
      debugPrint('加载设置出错: $e');
    }
  }
  
  // 开始自动保存
  void _startAutoSave() {
    // 取消已有的计时器
    _autoSaveTimer?.cancel();
    
    // 计算保存间隔（转换为毫秒）
    int milliseconds;
    if (_autoSaveUnit == 'min') {
      milliseconds = (_autoSaveInterval * 60 * 1000).toInt(); // 分钟转毫秒
    } else {
      milliseconds = (_autoSaveInterval * 1000).toInt(); // 秒转毫秒
    }
    
    // 设置新的计时器
    _autoSaveTimer = Timer.periodic(
      Duration(milliseconds: milliseconds),
      (_) {
        // 在Timer回调中添加mounted检查
        if (mounted) {
          _autoSave();
        }
      }
    );
    
    debugPrint('自动保存已启用，间隔 $_autoSaveInterval $_autoSaveUnit (${milliseconds}毫秒)');
  }
  
  // 检查未连接的视频节点
  void _checkDisconnectedVideoNodes() {
    // 添加mounted检查
    if (!mounted) return;
    
    int count = 0;
    for (final element in dashboard.elements) {
      // 只检查视频类型的节点
      if (element.kind == ElementKind.video) {
        bool hasLeftConnection = false;
        
        // 遍历所有元素，检查是否有连接到该元素
        for (final srcElement in dashboard.elements) {
          // 检查srcElement是否有连接到当前视频节点
          for (final connection in srcElement.next) {
            if (connection.destElementId == element.id) {
              hasLeftConnection = true;
              break;
            }
          }
          if (hasLeftConnection) break;
        }
        
        // 如果没有左侧连接，增加计数
        if (!hasLeftConnection) {
          count++;
        }
      }
    }
    
    // 再次检查mounted后更新状态
    if (mounted) {
      setState(() {
        _disconnectedVideoCount = count;
      });
    }
    
    debugPrint('未连接视频节点数量: $_disconnectedVideoCount');
  }

  // 检查起点节点后视频节点数量
  void _checkStartNodeVideoCount() {
    // 添加mounted检查
    if (!mounted) return;
    
    int count = 0;
    
    // 先查找起点元素
    FlowElement? startElement;
    for (final element in dashboard.elements) {
      if (element.kind == ElementKind.start) {
        startElement = element;
        break;
      }
    }
    
    // 如果找到起点元素，检查连接到的视频节点数量
    if (startElement != null) {
      for (final connection in startElement.next) {
        // 找到连接的目标元素
        FlowElement? targetElement;
        for (final element in dashboard.elements) {
          if (element.id == connection.destElementId) {
            targetElement = element;
            break;
          }
        }
        
        // 如果目标元素是视频，增加计数
        if (targetElement != null && targetElement.kind == ElementKind.video) {
          count++;
        }
      }
    }
    
    // 再次检查mounted后更新状态
    if (mounted) {
      setState(() {
        _startNodeVideoCount = count;
      });
    }
    
    debugPrint('起点后视频节点数量: $_startNodeVideoCount');
  }

  // 执行所有检查
  void _runAllChecks() {
    // 添加mounted检查，确保widget仍然存在
    if (!mounted) return;
    
    // 只有在启用流程图检测时才执行检查
    if (_enableFlowchartCheck) {
      _checkDisconnectedVideoNodes();
      _checkStartNodeVideoCount();
    } else {
      // 如果禁用检测，将警告计数清零
      setState(() {
        _disconnectedVideoCount = 0;
        _startNodeVideoCount = 1; // 设为1，使警告不显示
      });
    }
  }

  // 自动保存函数
  Future<void> _autoSave() async {
    // 添加mounted检查，确保widget仍然存在
    if (!mounted) return;

    if (_isAutoSavePausedByUI) {
      debugPrint('自动保存因UI交互而暂停');
      return;
    }

    // 检查是否正在连线（通过DrawingArrow实例的状态判断）
    if (!DrawingArrow.instance.isZero()) {
      debugPrint('正在连线中，暂停自动保存');
      return;
    }

    if (projectName != null && projectName!.isNotEmpty) {
      try {
        final savePath = CreateGameView.getSavePath(projectName);
        final directory = Directory(savePath);
        if (!await directory.exists()) {
          await directory.create(recursive: true);
        }
        
        // 保存流程图
        saveDashboard(dashboard, directory);
        
        // 保存全局变量
        await _saveGlobalVariables();
        
        // 再次检查mounted，因为上面的异步操作可能耗时
        if (!mounted) return;
        
        // 执行所有检查
        _runAllChecks();
        
        debugPrint('自动保存成功: $savePath');
      } catch (e) {
        debugPrint('自动保存失败: $e');
      }
    }

    // 保存完成后，记录最近编辑项目
    if (widget.projectPath != null && widget.initialProjectName != null) {
      CreateGameView.saveRecentEditedProject(widget.projectPath!, widget.initialProjectName!);
    }
  }

  // 加载现有项目
  Future<void> _loadExistingProject() async {
    // 添加mounted检查
    if (!mounted) return;
    
    if (projectName != null && projectName!.isNotEmpty) {
      // 优先使用传入的projectPath，如果没有则通过项目名获取路径
      final savePath = widget.projectPath?.isNotEmpty == true 
          ? widget.projectPath! 
          : CreateGameView.getSavePath(projectName);
      
      final directory = Directory(savePath);
      if (await directory.exists()) {
        // 再次检查mounted，因为await可能耗时
        if (!mounted) return;
        
        dashboard.projectPath = savePath;
        dashboard.projectName = projectName!; // 设置projectName
        
        // 尝试加载项目文件
        final flowchartFile = File('${savePath}/FLOWCHART.json');
        if (await flowchartFile.exists()) {
          // 再次检查mounted
          if (!mounted) return;
          
          dashboard.loadDashboard('${savePath}/FLOWCHART.json');
          // 加载后更新起始节点文本
          _updateStartElementText();
          
          // 加载后执行所有检查
          _runAllChecks();
        }

        // 加载全局数值
        await _loadGlobalVariables();
        
        // 在使用context前检查widget是否仍然挂载
        if (mounted) {
          // 将消息保存为成员变量，避免在异步操作后使用BuildContext
          _pendingSnackBarMessage = S.current.projectLoaded + savePath;

          // 使用setState触发重建，在下一帧中显示SnackBar
          setState(() {
            _showPendingSnackBar = true;
          });
        }

        // 启动自动保存
        await _loadAutoSaveSettings();
        if (mounted) {
          _startAutoSave();
        }
      } else {
        // 使用更安全的方式显示SnackBar
        if (mounted) {
          // 将消息保存为成员变量，避免在异步操作后使用BuildContext
          _pendingSnackBarMessage = S.current.projectNotFound + savePath;

          // 使用setState触发重建，在下一帧中显示SnackBar
          setState(() {
            _showPendingSnackBar = true;
          });
        }
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _updateStartElementText();
  }

  // 更新起始节点文本为当前语言的本地化文本
  void _updateStartElementText() {
    // 检查是否已存在起始节点
    final existingStartElement = dashboard.elements.where((element) => element.kind == ElementKind.start).firstOrNull;
    
    if (existingStartElement != null) {
      // 如果存在起始节点，更新其文本为当前语言的本地化文本
      existingStartElement.text = S.of(context).startPoint;
    } else {
      // 如果不存在起始节点，添加默认起点矩形
      dashboard.addElement(
        FlowElement(
          position: const Offset(100, 300),
          size: const Size(100, 50),
          text: S.of(context).startPoint,
          handlerSize: 25,
          kind: ElementKind.start,
          projectPath: projectName != null ? CreateGameView.getSavePath(projectName) : '',
          handlers: [
            Handler.rightCenter,
          ],
        ),
      );
    }
  }

  @override
  void dispose() {
    // 取消自动保存计时器
    _autoSaveTimer?.cancel();
    _autoSaveTimer = null;
    
    // 释放播放器资源
    player.dispose();
    
    super.dispose();
  }

  /// Notifier for the tension slider
  final segmentedTension = ValueNotifier<double>(1);

  // 安全地显示SnackBar的方法
  void _safelyShowSnackBar(String message) {
    // 添加mounted检查
    if (!mounted) return;
    
    // 将消息保存为成员变量，避免在异步操作后使用BuildContext
    _pendingSnackBarMessage = message;

    // 使用setState触发重建，在下一帧中显示SnackBar
    setState(() {
      _showPendingSnackBar = true;
    });
  }

  // 加载全局数值
  Future<void> _loadGlobalVariables() async {
    // 添加mounted检查
    if (!mounted) return;
    
    if (projectName != null && projectName!.isNotEmpty) {
      try {
        final savePath = CreateGameView.getSavePath(projectName);
        
        // 从FLOWCHART.json文件中读取全局变量
        final flowchartFile = File('$savePath/FLOWCHART.json');
        if (await flowchartFile.exists()) {
          final content = await flowchartFile.readAsString();
          final Map<String, dynamic> flowchartData = jsonDecode(content) as Map<String, dynamic>;
          
          // 再次检查mounted，因为文件读取可能耗时
          if (!mounted) return;
          
          if (flowchartData.containsKey('globalVariables')) {
            final globalVarsList = flowchartData['globalVariables'] as List;
            if (mounted) {
              setState(() {
                globalVariables = globalVarsList.map((e) => GlobalVariable.fromJson(e)).toList();
              });
            }
            debugPrint('从FLOWCHART.json加载了 ${globalVariables.length} 个全局数值');
            
            // 同步流程图中的全局数值元素与全局变量列表
            _synchronizeGlobalVariables();
            
            // 从流程图中移除全局数值节点
            _removeGlobalValueNodes();
          }
        }
      } catch (e) {
        debugPrint('加载全局数值出错: $e');
      }
    }
  }
  
  // 同步全局变量和流程图元素
  void _synchronizeGlobalVariables() {
    // 添加mounted检查
    if (!mounted) return;
    
    // 遍历所有globalValue类型的元素
    for (final element in dashboard.elements) {
      if (element.kind == ElementKind.globalValue) {
        // 检查是否存在对应的全局变量
        final variable = globalVariables.firstWhere(
          (v) => v.name == element.text,
          orElse: () => GlobalVariable(
            name: element.text,
            value: element.serializedData ?? '0',
            type: 'number', // 默认为数字类型
          ),
        );
        
        // 如果变量不存在于全局变量列表中，则添加
        if (!globalVariables.any((v) => v.name == element.text)) {
          globalVariables.add(variable);
        }
      }
    }
    
    // 保存更新后的全局变量
    _saveGlobalVariables();
    
    // 使用Toast显示同步成功消息
    if (mounted) {
      _safelyShowSnackBar("全局变量已同步到流程图");
    }
  }

  // 保存全局数值
  Future<void> _saveGlobalVariables() async {
    // 添加mounted检查
    if (!mounted) return;
    
    if (projectName != null && projectName!.isNotEmpty) {
      try {
        // 将全局变量保存到流程图FLOWCHART.json中
        if (dashboard.projectPath != null && dashboard.projectPath!.isNotEmpty) {
          final directory = Directory(dashboard.projectPath!);
          if (!await directory.exists()) {
            await directory.create(recursive: true);
          }
          
          // 再次检查mounted，因为目录创建可能耗时
          if (!mounted) return;
          
          // 读取现有的FLOWCHART.json文件
          final flowchartFile = File('${dashboard.projectPath}/FLOWCHART.json');
          Map<String, dynamic> flowchartData = {};
          
          if (await flowchartFile.exists()) {
            final content = await flowchartFile.readAsString();
            flowchartData = jsonDecode(content) as Map<String, dynamic>;
          }
          
          // 再次检查mounted，因为文件读取可能耗时
          if (!mounted) return;
          
          // 更新globalVariables字段
          flowchartData['globalVariables'] = globalVariables.map((e) => e.toJson()).toList();
          
          // 保存更新后的流程图文件
          final jsonString = JsonEncoder.withIndent('  ').convert(flowchartData);
          await flowchartFile.writeAsString(jsonString);
          debugPrint('已将全局变量保存到流程图文件中');
        }
      } catch (e) {
        debugPrint('保存全局数值出错: $e');
      }
    }
  }

  // 辅助方法：根据值的字符串解析合适的类型
  dynamic _parseValueWithType(String value) {
    // 尝试解析为数字
    final numberValue = double.tryParse(value);
    if (numberValue != null) {
      return numberValue;
    }
    
    // 尝试解析为布尔值
    if (value.toLowerCase() == 'true') {
      return true;
    } else if (value.toLowerCase() == 'false') {
      return false;
    }
    
    // 默认作为字符串返回
    return value;
  }

  @override
  Widget build(BuildContext context) {
    // 如果有待显示的SnackBar消息，显示它
    if (_showPendingSnackBar && _pendingSnackBarMessage != null) {
      // 重置标志，避免重复显示
      _showPendingSnackBar = false;

      // 不需要再次调用_safelyShowSnackBar，因为_pendingSnackBarMessage已经设置好了
      // 这里只需要重置标志，避免重复显示
    }

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.blue,
        title: Text(projectName != null ? projectName! : S.of(context).createNewGame),
        actions: [
          // 添加视频按钮
          IconButton(
            tooltip: S.of(context).addVideo,
            onPressed: () async {
              final path = await pickVideoBytes();
              if (path == null) return;
              
              player.open(Media(path), play: false);
              // 保存当前context的引用，确保在异步操作后仍然有效
              final currentContext = context;
              player.stream.duration
                  .where((duration) => duration.inSeconds > 0)
                  .take(1)
                  .listen((duration) async {
                final hours = duration.inHours;
                final minutes = duration.inMinutes % 60;
                final seconds = duration.inSeconds % 60;
                final milliseconds = duration.inMilliseconds % 1000;
                // 增加毫秒精度，确保捕获的时长更准确
                String allTime = '${hours}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}.${milliseconds}';
                String startTime = "0:00:00.0";
                String endTime = allTime;
                
                final newElement = FlowElement(
                  position: Offset(dashboard.dashboardSize.width/2, dashboard.dashboardSize.height/2),
                  size: const Size(300, 150),
                  text: '${dashboard.elements.length}',
                  path: path,
                  allTime: allTime,
                  startTime: startTime,
                  endTime: endTime,
                  projectPath: dashboard.projectPath,
                  projectName: projectName ?? '',
                  handlerSize: 25,
                  kind: ElementKind.video,
                  handlers: [
                    Handler.leftCenter,
                    Handler.rightCenter,
                  ],
                );

                final savePath = CreateGameView.getSavePath(projectName);
                final directory = Directory(savePath);
                if (!await directory.exists()) {
                  await directory.create(recursive: true);
                }
                final String thumbnailPath = '$savePath/${newElement.id}.jpg';

                // 修改截图代码部分
                await player.setVolume(0);
                // 确保视频帧已经加载，跳转到视频的10%位置
                await player.seek(Duration(seconds: (duration.inSeconds * 0.1).round()));
                // 添加延迟确保视频帧已经渲染
                await Future.delayed(const Duration(milliseconds: 500));
                // 强制刷新一帧
                await player.play();
                await Future.delayed(const Duration(milliseconds: 100));
                await player.pause();
                // 然后再截图
                final bytes = await player.screenshot(format: 'image/jpeg');
                if (bytes != null) {
                  await File(thumbnailPath).writeAsBytes(bytes);
                }
                
                dashboard.addElement(newElement);
                
                // 添加新视频节点后执行所有检查
                _runAllChecks();

                // 使用保存的context引用，而不是可能已经无效的context
                if (currentContext.mounted) {
                  showDialog(
                    context: currentContext,
                    barrierColor: Colors.transparent,
                    barrierDismissible: false,
                    builder: (context) => Stack(
                    children: [
                      FloatingVideoWindow(element: newElement),
                    ],
                    ),
                  );
                }
              });
            },
            icon: const Icon(Icons.video_call_outlined),
          ),
          // 添加全局数值按钮
          IconButton(
            tooltip: S.of(context).addGlobalValue,
            onPressed: () {
              _showGlobalVariablesDialog();
            },
            icon: const Icon(Icons.numbers),
          ),
          // 保存游戏流程图按钮
          IconButton(
            tooltip: S.of(context).saveFlowchart,
            onPressed: () async {
              final savePath = CreateGameView.getSavePath(projectName);
              final directory = Directory(savePath);
              if (!await directory.exists()) {
                await directory.create(recursive: true);
              }
              
              // 检查是否正在连线
              if (!DrawingArrow.instance.isZero()) {
                if (mounted) {
                  _safelyShowSnackBar('请先完成连线操作');
                }
                return;
              }
              
              saveDashboard(dashboard, directory);
              
              // 保存时执行所有检查
              _runAllChecks();

              if (mounted) {
                _safelyShowSnackBar('${S.of(context).saveFlowchart}: $savePath');
              }
            },
            icon: const Icon(Icons.save_outlined),
          ),
          // 读取游戏流程图按钮
          IconButton(
            tooltip: S.of(context).loadFlowchart,
            onPressed: () async {
              final savePath = CreateGameView.getSavePath(projectName);
              final directory = Directory(savePath);
              final String loadPath = '${directory.path}/FLOWCHART.json';
              // 提前准备消息字符串，避免在异步操作后使用context
              final String notFoundMessage = '${S.of(context).fileNotFound}: $loadPath';
              final String loadedMessage = '${S.of(context).loadFlowchart}: $loadPath';

              final File flowchartFile = File(loadPath);
              if (!await flowchartFile.exists()) {
                if (mounted) {
                  _safelyShowSnackBar(notFoundMessage);
                }
              } else{
                loadDashboard(dashboard, directory);
                // 加载后更新起始节点文本
                _updateStartElementText();

                if (mounted) {
                  _safelyShowSnackBar(loadedMessage);
                }
              }
            },
            icon: const Icon(Icons.folder_open_outlined),
          ),
          // 上传到创意工坊按钮
          IconButton(
            tooltip: S.of(context).uploadWorkshop,
            onPressed: widget.enableSteamFeatures ? 
              () => SteamClient.instance.uploadToSteam(context, projectName ?? '') : 
              () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${S.of(context).uploadWorkshop}功能未启用，请重启编辑器'))
                );
              },
            icon: const Icon(Icons.upload_outlined),
          ),
          // 更新创意工坊按钮
          IconButton(
            tooltip: S.of(context).updateWorkshop,
            onPressed: widget.enableSteamFeatures ? 
              () async {
                // 显示一个对话框，让用户从自己上传的Steam创意工坊物品中选择
                SteamFiles? filesResult;
                // 提前准备错误消息，避免在异步操作后使用context
                final String errorMessage = S.of(context).updateWorkshopError + "获取创意工坊物品失败";
                final String noItemsMessage = S.of(context).noWorkshopItems;

                try {
                  final SteamClient steamClient = SteamClient.instance;
                  filesResult = await steamClient.getAllItems(
                    page: 1, 
                    sort: SteamUGCSort.updateTime,
                    userId: SteamClient.instance.userId,
                  );
                } catch (e) {
                  // 处理异常
                  if (mounted) {
                    _safelyShowSnackBar('${errorMessage}: ${e.toString()}');
                  }
                  return;
                }

                if (filesResult.files.isEmpty) {
                  if (mounted) {
                    _safelyShowSnackBar(noItemsMessage);
                  }
                  return;
                }
                
                // 弹出对话框让用户选择一个已有物品进行更新
                final selectedFile = await showDialog<SteamFile>(
                  context: context,
                  builder: (context) => AlertDialog(
                    backgroundColor: Colors.black87,
                    title: Text(S.of(context).selectWorkshopItemToUpdate, style: TextStyle(color: Colors.white)),
                    content: Container(
                      width: double.maxFinite,
                      height: 300,
                      child: ListView.builder(
                        itemCount: filesResult!.files.length,
                        itemBuilder: (context, index) {
                          final file = filesResult!.files[index];
                          return ListTile(
                            contentPadding: EdgeInsets.symmetric(vertical: 8.0),
                            leading: SizedBox(
                              width: 80,
                              height: 45,
                              child: file.cover.isNotEmpty 
                                  ? Image.network(file.cover, fit: BoxFit.cover)
                                  : Container(color: Colors.grey.shade800),
                            ),
                            title: Text(file.name, style: TextStyle(color: Colors.white)),
                            subtitle: Text(
                              '${S.of(context).updateTime}: ${file.updateTime.toLocal().toString().substring(0, 16)}',
                              style: TextStyle(color: Colors.white70),
                            ),
                            onTap: () {
                              Navigator.pop(context, file);
                            },
                          );
                        },
                      ),
                    ),
                    actions: [
                      TextButton(
                        child: Text(S.of(context).cancel, style: TextStyle(color: Colors.white)),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ],
                  ),
                );
                
                // 如果用户选择了一个物品，则更新它
                if (selectedFile != null) {
                  await SteamClient.instance.uploadToSteam(context, projectName ?? '', steamFile: selectedFile);
                }
              } : 
              () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${S.of(context).updateWorkshop}功能未启用，请重启编辑器'))
                );
              },
            icon: const Icon(Icons.update_outlined),
          ),
          // 保留原有的放大缩小按钮，放在最后
          IconButton(
            onPressed: () {
              dashboard.setZoomFactor(1.5 * dashboard.zoomFactor);
            },
            icon: const Icon(Icons.zoom_in),
          ),
          IconButton(
            onPressed: () {
              dashboard.setZoomFactor(dashboard.zoomFactor / 1.5);
            },
            icon: const Icon(Icons.zoom_out),
          ),
        ],
      ),
      backgroundColor: Colors.black12,
      
      body: Stack( // 使用 Stack 来放置多个子组件
        children: [
          Container(
            constraints: const BoxConstraints.expand(),
            child: FlowChart(
              dashboard: dashboard,
              onNewConnection: (p1, p2) {
                debugPrint('new connection');
                // 当添加新连接时执行所有检查
                _runAllChecks();
              },
              onDashboardTapped: (context, position) {
                debugPrint('Dashboard tapped $position');
                // 直接设置状态，不调用 setState
                _isAutoSavePausedByUI = false;
                debugPrint('画布点击，恢复自动保存');
                _displayDashboardMenu(context, position);
              },
              onScaleUpdate: (newScale) {
                debugPrint('Scale updated. new scale: $newScale');
              },
              onDashboardSecondaryTapped: (context, position) {
                debugPrint('Dashboard right clicked $position');
                // 直接设置状态，不调用 setState
                _isAutoSavePausedByUI = false;
                debugPrint('画布右键点击，恢复自动保存');
                _displayDashboardMenu(context, position);
              },
              onDashboardLongTapped: (context, position) {
                debugPrint('Dashboard long tapped $position');
              },
              onDashboardSecondaryLongTapped: (context, position) {
                debugPrint('Dashboard long tapped with mouse right click $position');
              },
              onElementLongPressed: (context, position, element) {
                debugPrint('Element with "${element.text}" text long pressed');
              },
              onElementSecondaryLongTapped: (context, position, element) {
                debugPrint('Element with "${element.text}" text long tapped with mouse right click');
              },
              onElementPressed: (context, position, element) {
                debugPrint('Element with "${element.text}" text pressed');
                // 如果是起点节点，不显示菜单
                if (element.kind != ElementKind.start) {
                  // 直接设置状态，不调用 setState
                  _isAutoSavePausedByUI = true;
                  _elementMenuActionWillOpenDialog = false; // 重置标志
                  debugPrint('节点点击，暂停自动保存');
                  _displayElementMenu(context, position, element);
                }
              },
              onElementSecondaryTapped: (context, position, element) {
                debugPrint('Element with "${element.text}" text pressed');
                // 如果是起点节点，不显示菜单
                if (element.kind != ElementKind.start) {
                  // 直接设置状态，不调用 setState
                  _isAutoSavePausedByUI = true;
                  _elementMenuActionWillOpenDialog = false; // 重置标志
                  debugPrint('节点右键点击，暂停自动保存');
                  _displayElementMenu(context, position, element);
                }
              },
              onHandlerPressed: (context, position, handler, element) {
                debugPrint('handler pressed: position $position handler $handler" of element $element');
                _displayHandlerMenu(position, handler, element);
              },
              onHandlerLongPressed: (context, position, handler, element) {
                debugPrint('handler long pressed: position $position handler $handler" of element $element');
              },
              onPivotSecondaryPressed: (context, pivot) {
                dashboard.removeDissection(pivot);
              },
            ),
          ),
          if (showVideo) // 条件显示视频窗口
            VideoWindow(
              controller: videoController,
              initialPosition: videoWindowPosition, // 使用位置变量
              onPositionChanged: (newPosition) {
                setState(() {
                  videoWindowPosition = newPosition; // 更新位置变量
                });
              },
            ),
          
          // 警告栏容器 - 只有在启用流程图检测时才显示
          if (_enableFlowchartCheck)
            Positioned(
              top: 10,
              right: 10,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // 未连接视频节点警告框
                  if (_disconnectedVideoCount > 0)
                    Container(
                      margin: const EdgeInsets.only(bottom: 10),
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(8.0),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 5,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.warning_amber_rounded, color: Colors.white),
                          const SizedBox(width: 8.0),
                          Text(
                            S.of(context).disconnectedVideoCount + _disconnectedVideoCount.toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  
                  // 起点视频节点警告框
                  if (_startNodeVideoCount != 1)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      decoration: BoxDecoration(
                        color: _startNodeVideoCount > 1 
                          ? Colors.orange.withOpacity(0.9)
                          : Colors.red.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(8.0),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.3),
                            blurRadius: 5,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.warning_amber_rounded, color: Colors.white),
                          const SizedBox(width: 8.0),
                          Text(
                            S.of(context).startNodeVideoCount + _startNodeVideoCount.toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: dashboard.recenter,
        child: const Icon(Icons.center_focus_strong),
      ),
    );
  }

  //*********************
  //* POPUP MENUS
  //*********************

  /// Display a drop down menu when tapping on a handler
  void _displayHandlerMenu(
    Offset position,
    Handler handler,
    FlowElement element,
  ) {
    StarMenuOverlay.displayStarMenu(
      context,
      StarMenu(
        params: StarMenuParameters(
          shape: MenuShape.linear,
          openDurationMs: 60,
          linearShapeParams: const LinearShapeParams(
            angle: 270,
            space: 10,
            alignment: LinearAlignment.left,
          ),
          onHoverScale: 1.1,
          useTouchAsCenter: true,
          centerOffset: position -
              Offset(
                dashboard.dashboardSize.width / 2,
                dashboard.dashboardSize.height / 2,
              ),
        ),
        onItemTapped: (index, controller) {
          if (index != 2) {
            controller.closeMenu!();
          }
        },
        items: [
          ActionChip(
            label: const Icon(Icons.delete),
            onPressed: () =>
                dashboard.removeElementConnection(element, handler),
          ),
          ActionChip(
            label: const Icon(Icons.control_point),
            onPressed: () {
              dashboard.dissectElementConnection(element, handler);
            },
          ),
          ValueListenableBuilder<double>(
            valueListenable: segmentedTension,
            builder: (_, tension, __) {
              return Wrap(
                children: [
                  ActionChip(
                    label: Text(S.of(context).segmented),
                    onPressed: () {
                      dashboard.setArrowStyleByHandler(
                        element,
                        handler,
                        ArrowStyle.segmented,
                        tension: tension,
                      );
                    },
                  ),
                  SizedBox(
                    width: 200,
                    child: Slider(
                      value: tension,
                      max: 3,
                      onChanged: (v) {
                        segmentedTension.value = v;
                        dashboard.setArrowStyleByHandler(
                          element,
                          handler,
                          ArrowStyle.segmented,
                          tension: v,
                        );
                      },
                    ),
                  ),
                ],
              );
            },
          ),
          ActionChip(
            label: Text(S.of(context).curved),
            onPressed: () {
              dashboard.setArrowStyleByHandler(
                element,
                handler,
                ArrowStyle.curve,
              );
            },
          ),
          ActionChip(
            label: Text(S.of(context).rectangular),
            onPressed: () {
              dashboard.setArrowStyleByHandler(
                element,
                handler,
                ArrowStyle.rectangular,
              );
            },
          ),
        ],
        parentContext: context,
      ),
    );
  }

  /// Display a drop down menu when tapping on an element
  void _displayElementMenu(
    BuildContext context,
    Offset position,
    FlowElement element,
  ) {
    // 1. 预先构建菜单项列表
    final List<Widget> menuWidgetItems = [];

    // "修改时间和封面" (InkWell)
    menuWidgetItems.add(InkWell(
      onTap: () async {
        _elementMenuActionWillOpenDialog = true; // 标记此操作将打开对话框
        // _isAutoSavePausedByUI 此时应为 true （由 onElementPressed 设置）
        await showDialog(
          context: context,
          barrierColor: Colors.transparent,
          barrierDismissible: false,
          builder: (context) => Stack(
            children: [
              FloatingVideoWindow(element: element),
            ],
          ),
        );
        // FloatingVideoWindow 关闭后，恢复自动保存
        if (mounted) {
          setState(() {
            _isAutoSavePausedByUI = false;
            _elementMenuActionWillOpenDialog = false; 
            debugPrint('修改时间和封面界面关闭，恢复自动保存。');
          });
        }
      },
      child: Text(S.of(context).modifyTimeAndCover), // '修改时间与封面'
    ));

    // "修改标题" (TextMenu)
    menuWidgetItems.add(TextMenu(
      element: element,
    ));

    // 添加选项及数值变化设置菜单，只在视频节点且有多个连接时显示
    if (element.kind == ElementKind.video && element.next.length > 1) {
      menuWidgetItems.add(GlobalValueSettingsMenu(
        element: element,
        dashboard: dashboard,
      ));
      // 添加分支设置选项，只在视频节点且有多个连接时显示
      menuWidgetItems.add(BranchSettingsMenu(
        element: element,
        dashboard: dashboard,
      ));
    }
    
    // 不再显示全局数值节点管理选项，因为流程图中已不显示全局数值
    if(element.kind != ElementKind.start) { // 使用kind来判断是否为起点元素
      menuWidgetItems.add(InkWell(
        onTap: () async {
          // 如果是视频节点，删除对应的截图文件
          if (element.kind == ElementKind.video) {
            final String thumbnailPath = '${element.projectPath}/${element.id}.jpg';
            final File thumbnailFile = File(thumbnailPath);
            if (await thumbnailFile.exists()) {
              try {
                await thumbnailFile.delete();
                debugPrint('已删除视频节点封面截图: $thumbnailPath');
              } catch (e) {
                debugPrint('删除视频节点封面截图失败: $e');
              }
            }
          }
          
          dashboard.removeElement(element);
          // 当删除元素时执行所有检查
          _runAllChecks();
        },
        child: Text(S.of(context).delete),
      ));
    }

    menuWidgetItems.add(InkWell(
      onTap: () {
        dashboard.removeElementConnections(element);
        // 当移除连接时执行所有检查
        _runAllChecks();
      },
      child: Text(S.of(context).removeAllConnections), // '去除所有连接'
    ));

    menuWidgetItems.add(InkWell(
      onTap: () {
        dashboard.setElementConnectable(element, !element.isConnectable);
        // 主动调用setState来刷新UI，显示connectable状态的变化。
        // StarMenu的关闭由onItemTapped处理。
        setState(() {}); 
      },
      child: Text('${S.of(context).toggleConnectable} (${element.isConnectable ? '✔' : '✘'})'),
    ));

    menuWidgetItems.add(InkWell(
      onTap: () {
        dashboard.setElementResizable(element, !element.isResizable);
        // 主动调用setState来刷新UI
        setState(() {});
      },
      child: Text('${S.of(context).toggleResizable} (${element.isResizable ? '✔' : '✘'})'),
    ));
    
    menuWidgetItems.add(ElementSettingsMenu(
      element: element,
    ));

    StarMenuOverlay.displayStarMenu(
      context,
      StarMenu(
        onStateChanged: (state) { // 使用 onStateChanged
          if (mounted && (state == MenuState.closed || state == MenuState.closing)) {
            if (_elementMenuActionWillOpenDialog) {
              // 对话框即将打开或已打开，自动保存应保持暂停状态。
              // 实际的自动保存恢复由对话框关闭逻辑处理。
              // _elementMenuActionWillOpenDialog 会在对话框关闭后被重置。
              debugPrint('元素菜单关闭/正在关闭，但因对话框操作，自动保存状态由对话框流程管理。');
            } else {
              // 菜单因其他原因关闭
              if (_isAutoSavePausedByUI) { // 只有在确实是暂停状态时才去恢复
                _isAutoSavePausedByUI = false;
                debugPrint('元素菜单关闭/正在关闭（通用原因），标记恢复自动保存。');
              }
            }
          }
        },
        params: StarMenuParameters(
          shape: MenuShape.linear,
          openDurationMs: 60,
          linearShapeParams: const LinearShapeParams(
            angle: 270,
            alignment: LinearAlignment.left,
            space: 10,
          ),
          onHoverScale: 1.1,
          centerOffset: position - const Offset(50, 0),
          boundaryBackground: BoundaryBackground(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Theme.of(context).cardColor,
              boxShadow: kElevationToShadow[6],
            ),
          ),
        ),
        onItemTapped: (index, controller) {
          // 获取被点击的 widget
          if (index < menuWidgetItems.length) {
            final tappedWidget = menuWidgetItems[index];

            // 根据类型判断是否关闭父菜单
            if (tappedWidget is TextMenu || tappedWidget is ElementSettingsMenu) {
              // 特别处理这两个最容易导致闪退的组件，完全不关闭菜单
              debugPrint('TextMenu 或 ElementSettingsMenu 被点击，不关闭菜单');
              // 不执行任何关闭操作
            } else if (tappedWidget is InkWell) {
              // 对于 InkWell，我们假设它们是简单操作或打开标准对话框，父菜单应关闭。
              controller.closeMenu!();
            } else if (tappedWidget is GlobalValueSettingsMenu ||
                       tappedWidget is BranchSettingsMenu) {
              // 这些菜单项尝试使用延迟关闭
              Future.delayed(Duration(milliseconds: 300), () {
                // 关闭菜单前确保controller和closeMenu仍然有效
                if (controller.closeMenu != null) {
                  controller.closeMenu!();
                }
              });
            } else {
              // 对于其他未知类型或未明确处理的类型，默认也关闭菜单
              controller.closeMenu!();
            }
          } else {
            // 索引超出范围，也关闭（不太可能发生）
            controller.closeMenu!();
          }
        },
        items: menuWidgetItems, // 使用预先构建的列表
        parentContext: context,
      ),
    );
  }

  /// Display a linear menu for the dashboard
  void _displayDashboardMenu(BuildContext context, Offset position) {
    StarMenuOverlay.displayStarMenu(
      context,
      StarMenu(
        params: StarMenuParameters(
          shape: MenuShape.linear,
          openDurationMs: 60,
          linearShapeParams: const LinearShapeParams(
            angle: 270,
            alignment: LinearAlignment.left,
            space: 10,
          ),
          // calculate the offset from the dashboard center
          centerOffset: position -
              Offset(
                dashboard.dashboardSize.width / 2,
                dashboard.dashboardSize.height / 2,
              ),
        ),
        onItemTapped: (index, controller) => controller.closeMenu!(),
        parentContext: context,
        items: [
          ActionChip(
            label: Text(S.of(context).addVideo), // '添加视频'
            onPressed: () async {
              final path = await pickVideoBytes();
              if (path == null) return;
              
              player.open(Media(path), play: false);
              // 保存当前context的引用，确保在异步操作后仍然有效
              final currentContext = context;
              player.stream.duration
                  .where((duration) => duration.inSeconds > 0)
                  .take(1)
                  .listen((duration) async {
                final hours = duration.inHours;
                final minutes = duration.inMinutes % 60;
                final seconds = duration.inSeconds % 60;
                final milliseconds = duration.inMilliseconds % 1000;
                // 增加毫秒精度，确保捕获的时长更准确
                String allTime = '${hours}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}.${milliseconds}';
                String startTime = "0:00:00.0";
                String endTime = allTime;
                
                final newElement = FlowElement(
                  position: position,
                  size: const Size(300, 150),
                  text: '${dashboard.elements.length}',
                  path: path,
                  allTime: allTime,
                  startTime: startTime,
                  endTime: endTime,
                  projectPath: dashboard.projectPath,
                  projectName: projectName ?? '',
                  handlerSize: 25,
                  kind: ElementKind.video,
                  handlers: [
                    Handler.leftCenter,
                    Handler.rightCenter,
                  ],
                );

                final savePath = CreateGameView.getSavePath(projectName);
                final directory = Directory(savePath);
                if (!await directory.exists()) {
                  await directory.create(recursive: true);
                }
                final String thumbnailPath = '$savePath/${newElement.id}.jpg';

                // 修改截图代码部分
                await player.setVolume(0);
                // 确保视频帧已经加载，跳转到视频的10%位置
                await player.seek(Duration(seconds: (duration.inSeconds * 0.1).round()));
                // 添加延迟确保视频帧已经渲染
                await Future.delayed(const Duration(milliseconds: 500));
                // 强制刷新一帧
                await player.play();
                await Future.delayed(const Duration(milliseconds: 100));
                await player.pause();
                // 然后再截图
                final bytes = await player.screenshot(format: 'image/jpeg');
                if (bytes != null) {
                  await File(thumbnailPath).writeAsBytes(bytes);
                }
                
                dashboard.addElement(newElement);
                
                // 添加新视频节点后执行所有检查
                _runAllChecks();

                // 使用保存的context引用，而不是可能已经无效的context
                if (currentContext.mounted) {
                  showDialog(
                    context: currentContext,
                    barrierColor: Colors.transparent,
                    barrierDismissible: false,
                    builder: (context) => Stack(
                    children: [
                      FloatingVideoWindow(element: newElement),
                    ],
                    ),
                  );
                }
              });
            },
          ),
          ActionChip(
            label: Text(S.of(context).addGlobalValue), // '添加全局数值'
            onPressed: () {
              _showGlobalVariablesDialog();
            },
          ),
          ActionChip(
            label: Text(S.of(context).saveFlowchart), // '保存游戏流程图'
            onPressed: () async {
              final savePath = CreateGameView.getSavePath(projectName);
              final directory = Directory(savePath);
              if (!await directory.exists()) {
                await directory.create(recursive: true);
              }
              saveDashboard(dashboard, directory);
              
              // 保存时执行所有检查
              _runAllChecks();

              if (mounted) {
                _safelyShowSnackBar('${S.of(context).saveFlowchart}: $savePath');
              }
            },
          ),
          ActionChip(
            label: Text(S.of(context).loadFlowchart), // '读取游戏流程图'
            onPressed: () async {
              final savePath = CreateGameView.getSavePath(projectName);
              final directory = Directory(savePath);
              final String loadPath = '${directory.path}/FLOWCHART.json';
              // 提前准备消息字符串，避免在异步操作后使用context
              final String notFoundMessage = '${S.of(context).fileNotFound}: $loadPath';
              final String loadedMessage = '${S.of(context).loadFlowchart}: $loadPath';

              final File flowchartFile = File(loadPath);
              if (!await flowchartFile.exists()) {
                if (mounted) {
                  _safelyShowSnackBar(notFoundMessage);
                }
              } else{
                loadDashboard(dashboard, directory);
                // 加载后更新起始节点文本
                _updateStartElementText();

                if (mounted) {
                  _safelyShowSnackBar(loadedMessage);
                }
              }
            },
          ),
          ActionChip(
            label: Text(S.of(context).uploadWorkshop), // '上传到创意工坊'
            onPressed: widget.enableSteamFeatures ?
              () => SteamClient.instance.uploadToSteam(context, projectName ?? '') :
              () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${S.of(context).uploadWorkshop}功能未启用，请重启编辑器'))
                );
              },
          ),
          ActionChip(
            label: Text(S.of(context).updateWorkshop), // '更新创意工坊'
            onPressed: widget.enableSteamFeatures ?
              () {
                // 使用一个单独的方法来处理更新创意工坊的逻辑
                _updateWorkshopItem();
              } :
              () {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('${S.of(context).updateWorkshop}功能未启用，请重启编辑器'))
                );
              },
          ),
        ],
      ),
    );
  }

  // 更新创意工坊物品
  Future<void> _updateWorkshopItem() async {
    // 添加mounted检查
    if (!mounted) return;
    
    // 显示一个对话框，让用户从自己上传的Steam创意工坊物品中选择
    SteamFiles? filesResult;

    // 提前准备错误消息，避免在异步操作后使用context
    final errorMessage = S.of(context).updateWorkshopError(S.of(context).failedToGetWorkshopItems);
    final noItemsMessage = S.of(context).noWorkshopItems;
    final selectWorkshopItemToUpdateText = S.of(context).selectWorkshopItemToUpdate;
    final updateTimeText = S.of(context).updateTime;
    final cancelText = S.of(context).cancel;

    // 保存当前context的引用，确保在异步操作后仍然有效
    final currentContext = context;

    try {
      final SteamClient steamClient = SteamClient.instance;
      filesResult = await steamClient.getAllItems(
        page: 1,
        sort: SteamUGCSort.updateTime,
        userId: SteamClient.instance.userId,
      );
    } catch (e) {
      // 处理异常
      if (mounted) {
        _safelyShowSnackBar('$errorMessage: ${e.toString()}');
      }
      return;
    }

    if (!mounted) return;

    if (filesResult.files.isEmpty) {
      if (mounted) {
        _safelyShowSnackBar(noItemsMessage);
      }
      return;
    }

    // 弹出对话框让用户选择一个已有物品进行更新
    final selectedFile = await showDialog<SteamFile>(
      context: currentContext,
      builder: (dialogContext) => AlertDialog(
        backgroundColor: Colors.black87,
        title: Text(selectWorkshopItemToUpdateText, style: const TextStyle(color: Colors.white)),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: ListView.builder(
            itemCount: filesResult!.files.length,
            itemBuilder: (itemContext, index) {
              final file = filesResult!.files[index];
              return ListTile(
                contentPadding: const EdgeInsets.symmetric(vertical: 8.0),
                leading: SizedBox(
                  width: 80,
                  height: 45,
                  child: file.cover.isNotEmpty
                      ? Image.network(file.cover, fit: BoxFit.cover)
                      : Container(color: Colors.grey.shade800),
                ),
                title: Text(file.name, style: const TextStyle(color: Colors.white)),
                subtitle: Text(
                  '$updateTimeText: ${file.updateTime.toLocal().toString().substring(0, 16)}',
                  style: const TextStyle(color: Colors.white70),
                ),
                onTap: () {
                  Navigator.pop(dialogContext, file);
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            child: Text(cancelText, style: const TextStyle(color: Colors.white)),
            onPressed: () => Navigator.pop(dialogContext),
          ),
        ],
      ),
    );

    // 如果用户选择了一个物品，则更新它
    if (selectedFile != null && mounted) {
      await SteamClient.instance.uploadToSteam(currentContext, projectName ?? '', steamFile: selectedFile);
    }
  }

  // 显示全局数值管理对话框
  void _showGlobalVariablesDialog() {
    showDialog(
      context: context,
      builder: (context) => GlobalVariablesDialog(
        globalVariables: globalVariables,
        onSave: (variables) {
          setState(() {
            globalVariables = variables;
          });
          _saveGlobalVariables();
          // 保存全局数值后自动保存流程图
          _autoSave();
        },
        onAddToFlowchart: (variable) {
          // 移除流程图元素创建逻辑，改为显示提示
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('全局数值不再显示在流程图上')),
          );
          Navigator.pop(context);
        },
      ),
    );
  }

  // 从流程图中清除全局数值节点
  void _removeGlobalValueNodes() {
    final elementsToRemove = dashboard.elements
        .where((element) => element.kind == ElementKind.globalValue)
        .toList();
    
    for (final element in elementsToRemove) {
      dashboard.removeElement(element);
    }
    
    if (elementsToRemove.isNotEmpty) {
      debugPrint('已从流程图中移除 ${elementsToRemove.length} 个全局数值节点');
    }
  }
}

// 全局数值管理对话框
class GlobalVariablesDialog extends StatefulWidget {
  final List<GlobalVariable> globalVariables;
  final Function(List<GlobalVariable>) onSave;
  final Function(GlobalVariable) onAddToFlowchart;

  const GlobalVariablesDialog({
    required this.globalVariables,
    required this.onSave,
    required this.onAddToFlowchart,
    Key? key,
  }) : super(key: key);

  @override
  _GlobalVariablesDialogState createState() => _GlobalVariablesDialogState();
}

class _GlobalVariablesDialogState extends State<GlobalVariablesDialog> {
  late List<GlobalVariable> _variables;
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _valueController = TextEditingController();
  
  // 定义类型常量
  static const String TYPE_NUMBER = 'number';
  static const String TYPE_TEXT = 'text';
  static const String TYPE_BOOLEAN = 'boolean';
  
  String _selectedType = TYPE_NUMBER;
  
  // 获取类型的本地化显示文本
  String _getLocalizedType(BuildContext context, String type) {
    switch (type) {
      case TYPE_NUMBER:
        return S.of(context).numberType;
      case TYPE_TEXT:
        return S.of(context).textType;
      case TYPE_BOOLEAN:
        return S.of(context).booleanType;
      default:
        return type;
    }
  }

  @override
  void initState() {
    super.initState();
    _variables = List.from(widget.globalVariables);
  }

  void _addVariable() {
    if (_formKey.currentState!.validate()) {
      dynamic value;
      switch (_selectedType) {
        case TYPE_NUMBER:
          value = double.tryParse(_valueController.text) ?? 0;
          break;
        case TYPE_TEXT:
          value = _valueController.text;
          break;
        case TYPE_BOOLEAN:
          value = _valueController.text.toLowerCase() == 'true';
          break;
      }

      setState(() {
        _variables.add(GlobalVariable(
          name: _nameController.text,
          value: value,
          type: _selectedType,
        ));
      });

      _nameController.clear();
      _valueController.clear();
    }
  }

  void _removeVariable(int index) {
    setState(() {
      _variables.removeAt(index);
    });
  }

  // 新增：显示编辑全局变量的对话框
  void _editVariableDialog(GlobalVariable variable, int index) {
    final editNameController = TextEditingController(text: variable.name);
    final editValueController = TextEditingController(text: variable.value.toString());
    String editSelectedType = variable.type;
    final editFormKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: Text(S.of(context).editGlobalValueTitle(variable.name)), // 使用已有的本地化字符串
          content: Form(
            key: editFormKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: editNameController,
                  decoration: InputDecoration(
                    labelText: S.of(context).variableName,
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return S.of(context).pleaseEnterVariableName;
                    }
                    // 检查名称是否与自身以外的其他变量冲突
                    if (_variables.asMap().entries.any((entry) => entry.key != index && entry.value.name == value)) {
                      return S.of(context).variableAlreadyExists;
                    }
                    return null;
                  },
                ),
                SizedBox(height: 8),
                StatefulBuilder(
                  builder: (BuildContext context, StateSetter setState) {
                    return DropdownButtonFormField<String>(
                      value: editSelectedType,
                      decoration: InputDecoration(
                        labelText: S.of(context).variableType,
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        DropdownMenuItem(value: TYPE_NUMBER, child: Text(_getLocalizedType(dialogContext, TYPE_NUMBER))),
                        DropdownMenuItem(value: TYPE_TEXT, child: Text(_getLocalizedType(dialogContext, TYPE_TEXT))),
                        DropdownMenuItem(value: TYPE_BOOLEAN, child: Text(_getLocalizedType(dialogContext, TYPE_BOOLEAN))),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() { // 使用StatefulBuilder的setState
                            editSelectedType = value;
                          });
                        }
                      },
                    );
                  }
                ),
                SizedBox(height: 8),
                TextFormField(
                  controller: editValueController,
                  decoration: InputDecoration(
                    labelText: S.of(context).valueLabel,
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return S.of(context).pleaseEnterValue;
                    }
                    if (editSelectedType == TYPE_NUMBER && double.tryParse(value) == null) {
                      return S.of(context).pleaseEnterValidNumber;
                    }
                    if (editSelectedType == TYPE_BOOLEAN && value.toLowerCase() != 'true' && value.toLowerCase() != 'false') {
                      return S.of(context).pleaseEnterTrueOrFalse;
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              child: Text(S.of(context).cancel),
              onPressed: () => Navigator.pop(dialogContext),
            ),
            TextButton(
              child: Text(S.of(context).saveSettings), // 使用已有的本地化字符串 'Save Settings'
              onPressed: () {
                if (editFormKey.currentState!.validate()) {
                  dynamic newValue;
                  switch (editSelectedType) {
                    case TYPE_NUMBER:
                      newValue = double.tryParse(editValueController.text) ?? 0;
                      break;
                    case TYPE_TEXT:
                      newValue = editValueController.text;
                      break;
                    case TYPE_BOOLEAN:
                      newValue = editValueController.text.toLowerCase() == 'true';
                      break;
                  }
                  setState(() {
                    _variables[index] = GlobalVariable(
                      name: editNameController.text,
                      value: newValue,
                      type: editSelectedType,
                    );
                  });
                  Navigator.pop(dialogContext);
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(S.of(context).globalValues),
      content: Container(
        width: 500,
        height: 400,
        child: Column(
          children: [
            Form(
              key: _formKey,
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _nameController,
                      decoration: InputDecoration(
                        labelText: S.of(context).variableName,
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return S.of(context).pleaseEnterVariableName;
                        }
                        if (_variables.any((v) => v.name == value)) {
                          return S.of(context).variableAlreadyExists;
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: DropdownButtonFormField<String>(
                      value: _selectedType,
                      decoration: InputDecoration(
                        labelText: S.of(context).variableType,
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        DropdownMenuItem(value: TYPE_NUMBER, child: Text(S.of(context).numberType)),
                        DropdownMenuItem(value: TYPE_TEXT, child: Text(S.of(context).textType)),
                        DropdownMenuItem(value: TYPE_BOOLEAN, child: Text(S.of(context).booleanType)),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _selectedType = value;
                          });
                        }
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    flex: 2,
                    child: TextFormField(
                      controller: _valueController,
                      decoration: InputDecoration(
                        labelText: S.of(context).valueLabel,
                        border: OutlineInputBorder(),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return S.of(context).pleaseEnterValue;
                        }
                        if (_selectedType == TYPE_NUMBER && double.tryParse(value) == null) {
                          return S.of(context).pleaseEnterValidNumber;
                        }
                        if (_selectedType == TYPE_BOOLEAN && value.toLowerCase() != 'true' && value.toLowerCase() != 'false') {
                          return S.of(context).pleaseEnterTrueOrFalse;
                        }
                        return null;
                      },
                    ),
                  ),
                  SizedBox(width: 8),
                  IconButton(
                    icon: Icon(Icons.add_circle, color: Colors.green),
                    onPressed: _addVariable,
                  ),
                ],
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _variables.length,
                itemBuilder: (context, index) {
                  final variable = _variables[index];
                  return ListTile(
                    title: Text('${variable.name} (${_getLocalizedType(context, variable.type)})'),
                    subtitle: Text('${variable.value}'),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        IconButton(
                          icon: Icon(Icons.edit, color: Colors.orange),
                          tooltip: S.of(context).editGlobalValue,
                          onPressed: () {
                            _editVariableDialog(variable, index); // 调用新的编辑方法
                          },
                        ),
                        IconButton(
                          icon: Icon(Icons.delete, color: Colors.red),
                          onPressed: () => _removeVariable(index),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(S.of(context).cancel),
        ),
        TextButton(
          onPressed: () {
            widget.onSave(_variables);
            Navigator.pop(context);
          },
          child: Text(S.of(context).save),
        ),
      ],
    );
  }
}
