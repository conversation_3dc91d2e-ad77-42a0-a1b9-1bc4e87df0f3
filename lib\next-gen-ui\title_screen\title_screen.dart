// Copyright 2023 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

import 'dart:math';
import 'dart:ui';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:ve/generated/l10n.dart';

import '../assets.dart';
import '../orb_shader/orb_shader_config.dart';
import '../orb_shader/orb_shader_widget.dart';
import '../styles.dart';
import 'particle_overlay.dart';
import 'title_screen_ui.dart';
import 'load_archive_screen.dart';
import 'flow_chart_screen.dart';
import 'settings_screen.dart';
import '../../utils/workshop_path.dart';
import '../../utils/save_archive.dart';
import '../../UI/Views/open/open_game_view.dart' as open_game_view;

class TitleScreen extends StatefulWidget {
  final String projectPath;
  final String projectName;
  final String workshopItemId;
  final VoidCallback onStartPressed;
  final bool showBackButton;

  const TitleScreen({
    super.key,
    required this.projectPath,
    required this.projectName,
    required this.workshopItemId,
    required this.onStartPressed,
    this.showBackButton = false,
  });

  @override
  State<TitleScreen> createState() => _TitleScreenState();
}

class _TitleScreenState extends State<TitleScreen>
    with SingleTickerProviderStateMixin {
  final _orbKey = GlobalKey<OrbShaderWidgetState>();

  /// Editable Settings
  /// 0-1, receive lighting strength
  final _minReceiveLightAmt = .35;
  final _maxReceiveLightAmt = .7;

  /// 0-1, emit lighting strength
  final _minEmitLightAmt = .5;
  final _maxEmitLightAmt = 1;

  /// Internal
  var _mousePos = Offset.zero;

  Color get _emitColor =>
      AppColors.emitColors[_optionOverride ?? _selectedOption];
  Color get _orbColor =>
      AppColors.orbColors[_optionOverride ?? _selectedOption];

  /// Currently selected option
  int _selectedOption = 0;

  /// Currently focused option (if any)
  int? _optionOverride;
  double _orbEnergy = 0;
  double _minOrbEnergy = 0;

  double get _finalReceiveLightAmt {
    final light =
        lerpDouble(_minReceiveLightAmt, _maxReceiveLightAmt, _orbEnergy) ?? 0;
    return light + _pulseEffect.value * .05 * _orbEnergy;
  }

  double get _finalEmitLightAmt {
    return lerpDouble(_minEmitLightAmt, _maxEmitLightAmt, _orbEnergy) ?? 0;
  }

  late final _pulseEffect = AnimationController(
    vsync: this,
    duration: _getRndPulseDuration(),
    lowerBound: -1,
    upperBound: 1,
  );

  Duration _getRndPulseDuration() => 100.ms + 200.ms * Random().nextDouble();

  double _getMinEnergyForOption(int option) => switch (option) {
        1 => 0.3,
        2 => 0.6,
        _ => 0,
      };

  @override
  void initState() {
    super.initState();
    _pulseEffect.forward();
    _pulseEffect.addListener(_handlePulseEffectUpdate);
  }

  void _handlePulseEffectUpdate() {
    if (_pulseEffect.status == AnimationStatus.completed) {
      _pulseEffect.reverse();
      _pulseEffect.duration = _getRndPulseDuration();
    } else if (_pulseEffect.status == AnimationStatus.dismissed) {
      _pulseEffect.duration = _getRndPulseDuration();
      _pulseEffect.forward();
    }
  }

  void _handleOptionPressed(int value) {
    setState(() => _selectedOption = value);
    _bumpMinEnergy();
    
    // 处理不同选项的功能
    switch (value) {
      case 0: // 读取存档
        _openLoadArchiveScreen();
        break;
      case 1: // 查看流程图
        _openFlowChartScreen();
        break;
      case 2: // 设置
        _openSettingsScreen();
        break;
    }
  }

  Future<void> _bumpMinEnergy([double amount = 0.1]) async {
    setState(() {
      _minOrbEnergy = _getMinEnergyForOption(_selectedOption) + amount;
    });
    await Future<void>.delayed(.2.seconds);
    setState(() {
      _minOrbEnergy = _getMinEnergyForOption(_selectedOption);
    });
  }

  void _handleStartPressed() {
    _bumpMinEnergy(0.3);
    widget.onStartPressed();
  }
  
  // 打开读取存档界面
  void _openLoadArchiveScreen() {
    // 使用Navigator打开读取存档界面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _buildLoadArchiveScreen(),
      ),
    );
  }
  
  // 构建读取存档界面
  Widget _buildLoadArchiveScreen() {
    // 使用新创建的LoadArchiveScreen组件
    return LoadArchiveScreen(
      projectPath: widget.projectPath,
      projectName: widget.projectName,
      workshopItemId: widget.workshopItemId,
    );
  }
  
  // 打开流程图界面
  void _openFlowChartScreen() {
    // 使用Navigator打开流程图界面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _buildFlowChartScreen(),
      ),
    );
  }
  
  // 构建流程图界面
  Widget _buildFlowChartScreen() {
    return _showFlowChart();
  }
  
  // 查看流程图功能
  Widget _showFlowChart() {
    try {
      if (widget.projectPath.isEmpty) {
        // 如果没有项目路径，返回空白页面
        return Scaffold(
          appBar: AppBar(title: Text(S.of(context).flowChart)),
          body: Center(child: Text(S.of(context).pleaseSelectProject)),
        );
      }
      
      // 创建Dashboard实例
      final dashboard = Dashboard(projectPath: widget.projectPath, projectName: widget.projectName);
      
      // 加载FLOWCHART.json文件
      final String jsonFilePath;
      final isWorkshopItem = widget.workshopItemId != '0';
      
      if (isWorkshopItem) {
        jsonFilePath = WorkshopPath.getFlowchartPath(widget.workshopItemId);
      } else {
        jsonFilePath = '${widget.projectPath}/FLOWCHART.json';
      }
      
      // 读取并解析FLOWCHART.json文件
      final jsonFile = File(jsonFilePath);
      if (!jsonFile.existsSync()) {
        return Scaffold(
          appBar: AppBar(title: Text(S.of(context).flowChart)),
          body: Center(child: Text(S.of(context).fileNotFoundWithPath + jsonFilePath)),
        );
      }
      
      // 读取并解析FLOWCHART.json文件
      final String response = jsonFile.readAsStringSync();
      final data = json.decode(response);
      data['projectPath'] = widget.projectPath;
      data['projectName'] = widget.projectName;
      
      // 加载流程图数据
      dashboard.loadDashboardData(data);
      
      // 更新起始节点文本为当前语言的本地化文本
      _updateStartElementText(dashboard);
      
      // 设置为播放模式，与视频播放完毕后显示的流程图保持一致
      dashboard.setPlayMode(true);
      
      // 解析元素列表
      final elements = (data['elements'] as List)
          .map((element) => open_game_view.Element.fromJson(element as Map<String, dynamic>))
          .toList();
      
      // 获取起始元素索引
      final startIndex = elements.indexWhere((element) => element.kind == 0);
      Set<String> watchedNodeIds = {};
      
      // 尝试加载已观看节点信息
      try {
        if (startIndex != -1) {
          final startElementId = elements[startIndex].id;
          // 加载存档是异步操作，需要使用await
          SaveArchive.loadArchive(startElementId).then((projectArchive) {
            if (projectArchive != null) {
              // 加载已观看节点列表
              watchedNodeIds = projectArchive.videoQueue
                  .where((node) => node.completed)
                  .map((node) => node.nodeId)
                  .toSet();
              
              // 设置已观看节点
              dashboard.setWatchedNodeIds(watchedNodeIds);
            }
          });
        }
      } catch (e) {
        print('加载已观看节点信息失败: $e');
        // 即使加载已观看节点失败，也继续显示流程图
      }
      
      // 显示流程图
      return Scaffold(
        appBar: AppBar(title: Text(S.of(context).flowChart)),
        body: FlowChart(
          dashboard: dashboard,
          onElementPressed: (context, position, element) {
            // 查找元素索引
            final elementIndex = elements.indexWhere((e) => e.id == element.id);
            if (elementIndex != -1) {
              // 判断节点是否是起始节点或已观看节点
              final isStartNode = element.kind == ElementKind.start;
              final isNodeWatched = watchedNodeIds.contains(element.id);
              
              if (isStartNode || isNodeWatched) {
                // 打开视频播放界面，传递节点ID
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => open_game_view.OpenGameView(
                      projectPath: widget.projectPath,
                      projectName: widget.projectName,
                      workshopItemId: widget.workshopItemId,
                      initialNodeId: element.id, // 传递选中的节点ID
                    ),
                  ),
                ).then((_) {
                  // 返回后刷新流程图以更新已观看节点
                  _refreshFlowChart(dashboard);
                });
              } else {
                // 显示未解锁提示
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text(S.of(context).videoNodeLocked)),
                );
              }
            } else {
              print('未找到节点ID: ${element.id}');
            }
          },
        ),
      );
    } catch (e) {
      return Scaffold(
        appBar: AppBar(title: Text(S.of(context).flowChart)),
        body: Center(child: Text(S.of(context).openFlowChartFailed + e.toString())),
      );
    }
  }
  
  // 更新起始节点文本为当前语言的本地化文本
  void _updateStartElementText(Dashboard dashboard) {
    // 检查是否已存在起始节点
    final existingStartElement = dashboard.elements.where((element) => element.kind == ElementKind.start).firstOrNull;
    
    if (existingStartElement != null) {
      // 如果存在起始节点，更新其文本为当前语言的本地化文本
      existingStartElement.text = S.of(context).startPoint;
    }
  }
  
  // 刷新流程图
  void _refreshFlowChart(Dashboard dashboard) async {
    try {
      // 根据项目类型选择正确的FLOWCHART.json文件路径
      final String jsonFilePath;
      final isWorkshopItem = widget.workshopItemId != '0';
      
      if (isWorkshopItem) {
        jsonFilePath = WorkshopPath.getFlowchartPath(widget.workshopItemId);
      } else {
        jsonFilePath = '${widget.projectPath}/FLOWCHART.json';
      }
      
      // 重新加载元素并查找起始元素
      final jsonFile = File(jsonFilePath);
      if (jsonFile.existsSync()) {
        final String response = jsonFile.readAsStringSync();
        final data = json.decode(response);
        
        // 重新加载流程图数据
        data['projectPath'] = widget.projectPath;
        data['projectName'] = widget.projectName;
        dashboard.loadDashboardData(data);
        
        // 更新起始节点文本为当前语言的本地化文本
        _updateStartElementText(dashboard);
        
        final elements = (data['elements'] as List)
            .map((element) => open_game_view.Element.fromJson(element as Map<String, dynamic>))
            .toList();
        final startIndex = elements.indexWhere((element) => element.kind == 0);
        
        if (startIndex != -1) {
          final startElementId = elements[startIndex].id;
          // 重新加载存档
          final projectArchive = await SaveArchive.loadArchive(startElementId);
          if (projectArchive != null) {
            // 加载已观看节点列表
            final watchedNodeIds = projectArchive.videoQueue
                .where((node) => node.completed)
                .map((node) => node.nodeId)
                .toSet();
            
            // 更新已观看节点
            dashboard.setWatchedNodeIds(watchedNodeIds);
          }
        }
      }
    } catch (e) {
      print('刷新流程图失败: $e');
    }
  }
  
  // 打开设置界面
  void _openSettingsScreen() {
    // 使用Navigator打开设置界面
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => _buildSettingsScreen(),
      ),
    );
  }
  
  // 构建设置界面
  Widget _buildSettingsScreen() {
    // 使用新创建的SettingsScreen组件
    return SettingsScreen(
      projectId: widget.projectPath,
    );
  }

  void _handleOptionFocused(int? value) {
    setState(() {
      _optionOverride = value;
      if (value == null) {
        _minOrbEnergy = _getMinEnergyForOption(_selectedOption);
      } else {
        _minOrbEnergy = _getMinEnergyForOption(value);
      }
    });
  }

  /// Update mouse position so the orbWidget can use it, doing it here prevents
  /// btns from blocking the mouse-move events in the widget itself.
  void _handleMouseMove(PointerHoverEvent e) {
    setState(() {
      _mousePos = e.localPosition;
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: _handleMouseMove,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SizedBox.expand( // 确保填充所有可用空间
          child: _AnimatedColors(
            orbColor: _orbColor,
            emitColor: _emitColor,
            builder: (_, orbColor, emitColor) {
              return Stack(
                fit: StackFit.expand, // 确保Stack填充所有可用空间
                children: [
                  /// Bg-Base
                  Positioned.fill(child: Image.asset(AssetPaths.titleBgBase, fit: BoxFit.cover)),

                  /// Bg-Receive
                  Positioned.fill(
                    child: _LitImage(
                      color: orbColor,
                      imgSrc: AssetPaths.titleBgReceive,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalReceiveLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// Orb
                  Positioned.fill(
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        // Orb
                        OrbShaderWidget(
                          key: _orbKey,
                          mousePos: _mousePos,
                          minEnergy: _minOrbEnergy,
                          config: OrbShaderConfig(
                            ambientLightColor: orbColor,
                            materialColor: orbColor,
                            lightColor: orbColor,
                          ),
                          onUpdate: (energy) => setState(() {
                            _orbEnergy = energy;
                          }),
                        ),
                      ],
                    ),
                  ),

                  /// Mg-Base
                  Positioned.fill(
                    child: _LitImage(
                      imgSrc: AssetPaths.titleMgBase,
                      color: orbColor,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalReceiveLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// Mg-Receive
                  Positioned.fill(
                    child: _LitImage(
                      imgSrc: AssetPaths.titleMgReceive,
                      color: orbColor,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalReceiveLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// Mg-Emit
                  Positioned.fill(
                    child: _LitImage(
                      imgSrc: AssetPaths.titleMgEmit,
                      color: emitColor,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalEmitLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// Particle Field
                  Positioned.fill(
                    child: IgnorePointer(
                      child: ParticleOverlay(
                        color: orbColor,
                        energy: _orbEnergy,
                      ),
                    ),
                  ),

                  /// Fg-Rocks
                  Positioned.fill(child: Image.asset(AssetPaths.titleFgBase, fit: BoxFit.cover)),

                  /// Fg-Receive
                  Positioned.fill(
                    child: _LitImage(
                      imgSrc: AssetPaths.titleFgReceive,
                      color: orbColor,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalReceiveLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// Fg-Emit
                  Positioned.fill(
                    child: _LitImage(
                      imgSrc: AssetPaths.titleFgEmit,
                      color: emitColor,
                      pulseEffect: _pulseEffect,
                      lightAmt: _finalEmitLightAmt,
                      fit: BoxFit.cover,
                    ),
                  ),

                  /// UI
                  Positioned.fill(
                    child: TitleScreenUi(
                      difficulty: _selectedOption,
                      onDifficultyFocused: _handleOptionFocused,
                      onDifficultyPressed: _handleOptionPressed,
                      onStartPressed: _handleStartPressed,
                      projectName: widget.projectName,
                    ),
                  ),
                  
                  if (widget.showBackButton)
                    Positioned(
                      top: 20,
                      left: 20,
                      child: FloatingActionButton(
                        mini: true,
                        backgroundColor: Colors.black.withOpacity(0.5),
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                      ),
                    ),
                ],
              ).animate().fadeIn(duration: 1.seconds, delay: .3.seconds);
            },
          ),
        ),
      ),
    );
  }
}

class _LitImage extends StatelessWidget {
  const _LitImage({
    required this.color,
    required this.imgSrc,
    required this.pulseEffect,
    required this.lightAmt,
    this.fit = BoxFit.none, // 添加fit参数
  });
  final Color color;
  final String imgSrc;
  final AnimationController pulseEffect;
  final double lightAmt;
  final BoxFit fit; // 新增参数

  @override
  Widget build(BuildContext context) {
    final hsl = HSLColor.fromColor(color);
    return ListenableBuilder(
      listenable: pulseEffect,
      builder: (context, child) {
        return Image.asset(
          imgSrc,
          color: hsl.withLightness(hsl.lightness * lightAmt).toColor(),
          colorBlendMode: BlendMode.modulate,
          fit: fit, // 使用fit参数
        );
      },
    );
  }
}

class _AnimatedColors extends StatelessWidget {
  const _AnimatedColors({
    required this.emitColor,
    required this.orbColor,
    required this.builder,
  });

  final Color emitColor;
  final Color orbColor;

  final Widget Function(BuildContext context, Color orbColor, Color emitColor)
      builder;

  @override
  Widget build(BuildContext context) {
    final duration = .5.seconds;
    return TweenAnimationBuilder(
      tween: ColorTween(begin: emitColor, end: emitColor),
      duration: duration,
      builder: (_, emitColor, __) {
        return TweenAnimationBuilder(
          tween: ColorTween(begin: orbColor, end: orbColor),
          duration: duration,
          builder: (context, orbColor, __) {
            return builder(context, orbColor!, emitColor!);
          },
        );
      },
    );
  }
}
