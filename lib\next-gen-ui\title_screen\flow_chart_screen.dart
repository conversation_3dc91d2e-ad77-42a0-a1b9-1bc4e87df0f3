import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:ve/generated/l10n.dart';
import 'package:ve/utils/workshop_path.dart';
import 'package:ve/utils/coin_manager.dart';
import 'package:ve/UI/shared/coin_animation.dart';
import '../styles.dart';
import '../../utils/save_archive.dart';
import '../../UI/Views/open/open_game_view.dart' as open_game_view;

class FlowChartScreen extends StatefulWidget {
  final String projectPath;
  final String projectName;
  final String workshopItemId;

  const FlowChartScreen({
    Key? key,
    required this.projectPath,
    required this.projectName,
    required this.workshopItemId,
  }) : super(key: key);

  @override
  _FlowChartScreenState createState() => _FlowChartScreenState();
}

class _FlowChartScreenState extends State<FlowChartScreen> {
  late Dashboard dashboard;
  bool _isLoading = true;
  String? _errorMessage;
  bool _showingCoinAnimation = false;
  bool _coinRewardChecked = false;

  @override
  void initState() {
    super.initState();
    dashboard = Dashboard(projectPath: widget.projectPath, projectName: widget.projectName);
    _loadFlowChart();
    _checkAndRewardCoins();
  }
  
  /// 检查并奖励金币
  Future<void> _checkAndRewardCoins() async {
    if (_coinRewardChecked) return;
    
    // 使用项目路径作为唯一标识，但对于非工作坊项目，使用项目文件夹名称而非完整路径
    final projectId = widget.workshopItemId != '0' 
        ? widget.workshopItemId 
        : widget.projectPath.split(Platform.pathSeparator).last;
    
    // 检查是否已经奖励过金币
    final coinManager = CoinManager();
    final rewarded = await coinManager.rewardCoinsForProject(projectId);
    
    // 如果成功奖励（即首次完成），显示金币动画
    if (rewarded && mounted) {
      setState(() {
        _showingCoinAnimation = true;
        _coinRewardChecked = true;
      });
    } else {
      _coinRewardChecked = true;
    }
  }

  Future<void> _loadFlowChart() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // 根据项目类型选择正确的FLOWCHART.json文件路径
      final String jsonFilePath;
      final isWorkshopItem = widget.workshopItemId != '0';
      
      if (isWorkshopItem) {
        jsonFilePath = WorkshopPath.getFlowchartPath(widget.workshopItemId);
      } else {
        jsonFilePath = '${widget.projectPath}/FLOWCHART.json';
      }
      
      print('加载FLOWCHART.json文件: $jsonFilePath');
      
      final File jsonFile = File(jsonFilePath);
      if (!await jsonFile.exists()) {
        setState(() {
          _isLoading = false;
          _errorMessage = S.of(context).flowchartFileNotFound;
        });
        return;
      } 
      
      final String response = await jsonFile.readAsString();
      final data = await json.decode(response);
      data['projectPath'] = widget.projectPath;
      data['projectName'] = widget.projectName;
    
      dashboard.loadDashboardData(data);
      
      // 更新起始节点文本为当前语言的本地化文本
      _updateStartElementText(dashboard);
      
      // 设置为播放模式，与视频播放完毕后显示的流程图保持一致
      dashboard.setPlayMode(true);
      
      // 解析元素列表
      final elements = (data['elements'] as List)
          .map((element) => open_game_view.Element.fromJson(element as Map<String, dynamic>))
          .toList();
      
      // 获取起始元素索引并加载已观看节点信息
      final startIndex = elements.indexWhere((element) => element.kind == 0);
      if (startIndex != -1) {
        final startElementId = elements[startIndex].id;
        // 加载存档
        final projectArchive = await SaveArchive.loadArchive(startElementId);
        if (projectArchive != null) {
          // 加载已观看节点列表
          final watchedNodeIds = projectArchive.videoQueue
              .where((node) => node.completed)
              .map((node) => node.nodeId)
              .toSet();
          
          // 设置已观看节点
          dashboard.setWatchedNodeIds(watchedNodeIds);
        }
      }
      
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = S.of(context).flowchartLoadError + e.toString();
      });
    }
  }

  // 更新起始节点文本为当前语言的本地化文本
  void _updateStartElementText(Dashboard dashboard) {
    // 检查是否已存在起始节点
    final existingStartElement = dashboard.elements.where((element) => element.kind == ElementKind.start).firstOrNull;
    
    if (existingStartElement != null) {
      // 如果存在起始节点，更新其文本为当前语言的本地化文本
      existingStartElement.text = S.of(context).startPoint;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(S.of(context).viewFlowchart, style: TextStyles.h3.copyWith(color: Colors.black)),
        backgroundColor: Colors.white,
        iconTheme: const IconThemeData(color: Colors.black),
      ),
      body: Stack(
        children: [
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _errorMessage != null
                  ? Center(child: Text(_errorMessage!, style: TextStyles.body))
                  : Container(
                      color: Colors.white,
                      child: FlowChart(
                        dashboard: dashboard,
                        onElementPressed: (context, position, element) {
                          // 获取元素列表
                          try {
                            // 加载元素
                            final jsonFilePath = widget.workshopItemId != '0'
                                ? WorkshopPath.getFlowchartPath(widget.workshopItemId)
                                : '${widget.projectPath}/FLOWCHART.json';
                            
                            final jsonFile = File(jsonFilePath);
                            if (jsonFile.existsSync()) {
                              final String response = jsonFile.readAsStringSync();
                              final data = json.decode(response);
                              final elements = (data['elements'] as List)
                                  .map((element) => open_game_view.Element.fromJson(element as Map<String, dynamic>))
                                  .toList();
                              
                              // 查找对应的OpenGameView中的Element
                              // 通过ID匹配当前流程图节点和OpenGameView中的节点
                              final flowElementId = element.id;
                              
                              // 获取起始元素ID以加载存档
                              final startIndex = elements.indexWhere((element) => element.kind == 0);
                              if (startIndex != -1) {
                                final startElementId = elements[startIndex].id;
                                
                                // 异步加载存档和检查节点
                                SaveArchive.loadArchive(startElementId).then((projectArchive) {
                                  if (projectArchive != null) {
                                    // 获取已观看节点列表
                                    final watchedNodeIds = projectArchive.videoQueue
                                        .where((node) => node.completed)
                                        .map((node) => node.nodeId)
                                        .toSet();
                                    
                                    // 判断节点是否是起始节点或已观看节点
                                    final bool isStartNode = element.kind == ElementKind.start;
                                    final bool isNodeWatched = watchedNodeIds.contains(flowElementId);
                                    
                                    if (isStartNode || isNodeWatched) {
                                      // 打开视频播放界面，传递节点ID
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) => open_game_view.OpenGameView(
                                            projectPath: widget.projectPath,
                                            projectName: widget.projectName,
                                            workshopItemId: widget.workshopItemId,
                                            initialNodeId: flowElementId, // 传递选中的节点ID
                                          ),
                                        ),
                                      ).then((_) {
                                        // 返回后刷新流程图
                                        _loadFlowChart();
                                      });
                                    } else {
                                      // 显示未解锁提示
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text(S.of(context).videoNodeLocked)),
                                      );
                                      
                                      // 显示节点详情对话框
                                      _showNodeDetailsDialog(element);
                                    }
                                  } else {
                                    // 如果没有存档，只有起始节点可以点击
                                    if (element.kind == ElementKind.start) {
                                      // 打开视频播放界面从起始节点开始
                                      Navigator.of(context).push(
                                        MaterialPageRoute(
                                          builder: (context) => open_game_view.OpenGameView(
                                            projectPath: widget.projectPath,
                                            projectName: widget.projectName,
                                            workshopItemId: widget.workshopItemId,
                                            initialNodeId: flowElementId,
                                          ),
                                        ),
                                      ).then((_) {
                                        // 返回后刷新流程图
                                        _loadFlowChart();
                                      });
                                    } else {
                                      // 显示未解锁提示
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(content: Text(S.of(context).videoNodeLocked)),
                                      );
                                      
                                      // 显示节点详情对话框
                                      _showNodeDetailsDialog(element);
                                    }
                                  }
                                });
                              } else {
                                // 找不到起始节点，只显示详情对话框
                                _showNodeDetailsDialog(element);
                              }
                            } else {
                              // 流程图文件不存在，只显示详情对话框
                              _showNodeDetailsDialog(element);
                            }
                          } catch (e) {
                            print('处理节点点击时出错: $e');
                            // 发生错误时，显示详情对话框
                            _showNodeDetailsDialog(element);
                          }
                        },
                      ),
                    ),
          
          // 金币获取动画
          if (_showingCoinAnimation)
            CoinAnimation(
              coinAmount: 10,
              onAnimationComplete: () {
                if (mounted) {
                  setState(() {
                    _showingCoinAnimation = false;
                  });
                }
              },
            ),
        ],
      ),
    );
  }
  
  // 获取元素类型文本
  String _getElementTypeText(int kind) {
    switch (kind) {
      case 0:
        return S.of(context).startNode;
      case 1:
        return S.of(context).videoNode;
      case 2:
        return S.of(context).rectangleNode;
      case 3:
        return S.of(context).diamondNode;
      case 4:
        return S.of(context).storageNode;
      case 5:
        return S.of(context).ovalNode;
      case 6:
        return S.of(context).parallelogramNode;
      case 7:
        return S.of(context).hexagonNode;
      case 8:
        return S.of(context).imageNode;
      default:
        return S.of(context).unknownNodeType;
    }
  }

  void _showNodeDetailsDialog(FlowElement element) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(S.of(context).nodeDetails),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${S.of(context).elementId}: ${element.id}'),
            const SizedBox(height: 8),
            Text('${S.of(context).elementType}: ${_getElementTypeText(element.kind.index)}'),
            const SizedBox(height: 8),
            Text('${S.of(context).elementText}: ${element.text}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(S.of(context).close),
          ),
        ],
      ),
    );
  }
}