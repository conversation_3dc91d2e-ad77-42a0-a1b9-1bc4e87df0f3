// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for a messages locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'messages';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
    "AncientChinese": MessageLookupByLibrary.simpleMessage("Ancient Chinese"),
    "Anime": MessageLookupByLibrary.simpleMessage("Anime"),
    "Everyone": MessageLookupByLibrary.simpleMessage("Everyone"),
    "Landscape": MessageLookupByLibrary.simpleMessage("Landscape"),
    "NSFW": MessageLookupByLibrary.simpleMessage("NSFW"),
    "Other": MessageLookupByLibrary.simpleMessage("Other"),
    "Pixel": MessageLookupByLibrary.simpleMessage("Pixel"),
    "Portrait": MessageLookupByLibrary.simpleMessage("Portrait"),
    "Realistic": MessageLookupByLibrary.simpleMessage("Realistic"),
    "SearchWorkshop": MessageLookupByLibrary.simpleMessage(
      "Search in the workshop",
    ),
    "Square": MessageLookupByLibrary.simpleMessage("Square"),
    "Subscribe": MessageLookupByLibrary.simpleMessage("Subscribe"),
    "SubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Subscribe & download",
    ),
    "Subscribed": MessageLookupByLibrary.simpleMessage("Subscribed"),
    "Unsubscribed": MessageLookupByLibrary.simpleMessage("Unsubscribed"),
    "about": MessageLookupByLibrary.simpleMessage("About"),
    "achievementDisplay": MessageLookupByLibrary.simpleMessage(
      "Achievement Display",
    ),
    "achievements": MessageLookupByLibrary.simpleMessage("Achievements"),
    "activity": MessageLookupByLibrary.simpleMessage("Activity"),
    "add": MessageLookupByLibrary.simpleMessage("Add"),
    "addDiamond": MessageLookupByLibrary.simpleMessage("Add Diamond"),
    "addGlobalValue": MessageLookupByLibrary.simpleMessage("Add Global Value"),
    "addHexagon": MessageLookupByLibrary.simpleMessage("Add Hexagon"),
    "addImage": MessageLookupByLibrary.simpleMessage("Add Image"),
    "addOval": MessageLookupByLibrary.simpleMessage("Add Oval"),
    "addParallelogram": MessageLookupByLibrary.simpleMessage(
      "Add Parallelogram",
    ),
    "addRectangle": MessageLookupByLibrary.simpleMessage("Add Rectangle"),
    "addResizableRectangle": MessageLookupByLibrary.simpleMessage(
      "Add Resizable Rectangle",
    ),
    "addStorage": MessageLookupByLibrary.simpleMessage("Add Storage"),
    "addToFlowchart": MessageLookupByLibrary.simpleMessage("Add to Flowchart"),
    "addVariable": MessageLookupByLibrary.simpleMessage("Add Variable"),
    "addVideo": MessageLookupByLibrary.simpleMessage("Add Video"),
    "adultAgreementContent": MessageLookupByLibrary.simpleMessage(
      "The Steam Workshop contains co created content from players worldwide, which may involve content that is not suitable for viewing in public places.",
    ),
    "adultAgreementTitle": MessageLookupByLibrary.simpleMessage(
      "Please make sure you are over 18 years old",
    ),
    "ageRating": MessageLookupByLibrary.simpleMessage("Age Rating"),
    "allSettings": MessageLookupByLibrary.simpleMessage("All Settings"),
    "alreadyFavorited": MessageLookupByLibrary.simpleMessage(
      "Already favorited",
    ),
    "alreadyLiked": MessageLookupByLibrary.simpleMessage("Already liked"),
    "alreadySubscribed": MessageLookupByLibrary.simpleMessage(
      "Already subscribed",
    ),
    "apply": MessageLookupByLibrary.simpleMessage("Apply"),
    "archive": MessageLookupByLibrary.simpleMessage("Archive"),
    "archiveFileNotFound": MessageLookupByLibrary.simpleMessage(
      "Archive file does not exist",
    ),
    "archiveUpdatedForNode": MessageLookupByLibrary.simpleMessage(
      "Archive updated for node: ",
    ),
    "audioSettings": MessageLookupByLibrary.simpleMessage("Audio Settings"),
    "audioTrack": MessageLookupByLibrary.simpleMessage("Audio Track"),
    "autoFullScreenVideo": MessageLookupByLibrary.simpleMessage(
      "Auto Fullscreen Video",
    ),
    "autoSaveDescription": MessageLookupByLibrary.simpleMessage(
      "Automatically save game progress",
    ),
    "autoSaveGame": MessageLookupByLibrary.simpleMessage("Auto save game"),
    "autoSaveInterval": MessageLookupByLibrary.simpleMessage(
      "Auto save interval",
    ),
    "autoSaveProgress": MessageLookupByLibrary.simpleMessage(
      "Auto save progress",
    ),
    "autoSelect": MessageLookupByLibrary.simpleMessage("Auto Select"),
    "back": MessageLookupByLibrary.simpleMessage("Back"),
    "backgroundColor": MessageLookupByLibrary.simpleMessage("Background Color"),
    "booleanType": MessageLookupByLibrary.simpleMessage("Boolean"),
    "borderColor": MessageLookupByLibrary.simpleMessage("Border Color"),
    "branchIndexLabel": MessageLookupByLibrary.simpleMessage("Branch "),
    "branchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Branch settings and flowchart saved",
    ),
    "branchWithText": MessageLookupByLibrary.simpleMessage("Branch: "),
    "buttonDisplayTime": MessageLookupByLibrary.simpleMessage(
      "Button Display Time",
    ),
    "buttonDisplayTimeDescription": MessageLookupByLibrary.simpleMessage(
      "How many seconds before video ends to show branch buttons",
    ),
    "buttonDisplayTimeNote": MessageLookupByLibrary.simpleMessage(
      "When set to 0, buttons show only when video ends",
    ),
    "buttonOpacity": MessageLookupByLibrary.simpleMessage("Button Opacity"),
    "buttonPositionMiddle": MessageLookupByLibrary.simpleMessage(
      "%, Vertical: ",
    ),
    "buttonPositionPrefix": MessageLookupByLibrary.simpleMessage(
      "Horizontal: ",
    ),
    "buttonPositionSuffix": MessageLookupByLibrary.simpleMessage("%"),
    "buttonText": MessageLookupByLibrary.simpleMessage("Button Text"),
    "buttonTextOnly": MessageLookupByLibrary.simpleMessage(
      "Text Only (No Button Background)",
    ),
    "cancel": MessageLookupByLibrary.simpleMessage("Cancel"),
    "captureCurrentFrame": MessageLookupByLibrary.simpleMessage(
      "Capture Current Frame",
    ),
    "challenge": MessageLookupByLibrary.simpleMessage("Challenges"),
    "challengeName": MessageLookupByLibrary.simpleMessage("Challenge name"),
    "clickToSubscribeAndDownload": MessageLookupByLibrary.simpleMessage(
      "Click to subscribe and download",
    ),
    "close": MessageLookupByLibrary.simpleMessage("Close"),
    "closeSubtitle": MessageLookupByLibrary.simpleMessage("Close Subtitle"),
    "committingChanges": MessageLookupByLibrary.simpleMessage(
      "Committing changes",
    ),
    "completed": MessageLookupByLibrary.simpleMessage("Completion Status"),
    "confirm": MessageLookupByLibrary.simpleMessage("Confirm"),
    "confirmExitApp": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to exit the application?",
    ),
    "confirmUnsubscribe": MessageLookupByLibrary.simpleMessage(
      "Are you sure you want to unsubscribe?",
    ),
    "confirmUpdateWorkshopItem": MessageLookupByLibrary.simpleMessage(
      "Update Workshop Item",
    ),
    "confirmUpdateWorkshopItemDescription": MessageLookupByLibrary.simpleMessage(
      "You are about to update an existing workshop item. This will replace the current version with your changes.",
    ),
    "congratsEarnedCoins": MessageLookupByLibrary.simpleMessage(
      "Congratulations! You earned 10 coins!",
    ),
    "coverLoadFailed": MessageLookupByLibrary.simpleMessage(
      "Cover Load Failed",
    ),
    "createChallenge": MessageLookupByLibrary.simpleMessage("Create challenge"),
    "createNewGame": MessageLookupByLibrary.simpleMessage("Create Game"),
    "createTime": MessageLookupByLibrary.simpleMessage("Create Time"),
    "credits": MessageLookupByLibrary.simpleMessage("Credits"),
    "creditsAnimationDesigner": MessageLookupByLibrary.simpleMessage(
      "Animation Designer",
    ),
    "creditsArtDesigner": MessageLookupByLibrary.simpleMessage("Art Designer"),
    "creditsCodeWriter": MessageLookupByLibrary.simpleMessage("Code Writer"),
    "creditsCopywriter": MessageLookupByLibrary.simpleMessage("Copywriter"),
    "creditsCreativeSource": MessageLookupByLibrary.simpleMessage(
      "Creative Source",
    ),
    "creditsCreativeTitle": MessageLookupByLibrary.simpleMessage(
      "Creative & Planning",
    ),
    "creditsGameProducer": MessageLookupByLibrary.simpleMessage(
      "Game Producer",
    ),
    "creditsGameplayPlanner": MessageLookupByLibrary.simpleMessage(
      "Gameplay Planner",
    ),
    "creditsMarketingTitle": MessageLookupByLibrary.simpleMessage("Marketing"),
    "creditsProgrammingTitle": MessageLookupByLibrary.simpleMessage(
      "Programming",
    ),
    "creditsSoftwareArchitect": MessageLookupByLibrary.simpleMessage(
      "Software Architect",
    ),
    "creditsSoftwarePlanner": MessageLookupByLibrary.simpleMessage(
      "Software Planner",
    ),
    "creditsSpecialThanks": MessageLookupByLibrary.simpleMessage(
      "Special Thanks",
    ),
    "creditsSubtitle": MessageLookupByLibrary.simpleMessage(
      "View teams and contributors involved in software development",
    ),
    "creditsThanksTitle": MessageLookupByLibrary.simpleMessage(
      "Thanks for playing my game!",
    ),
    "creditsVideoProducer": MessageLookupByLibrary.simpleMessage(
      "Video Producer",
    ),
    "creditsVisualTitle": MessageLookupByLibrary.simpleMessage("Visual Design"),
    "currentPosition": MessageLookupByLibrary.simpleMessage("Current Position"),
    "curved": MessageLookupByLibrary.simpleMessage("Curved"),
    "customButtonPosition": MessageLookupByLibrary.simpleMessage(
      "Custom Button Position",
    ),
    "delete": MessageLookupByLibrary.simpleMessage("Delete"),
    "description": MessageLookupByLibrary.simpleMessage("Description"),
    "developer": MessageLookupByLibrary.simpleMessage("Developer"),
    "diamondNode": MessageLookupByLibrary.simpleMessage("Diamond Node"),
    "disconnectedVideoCount": MessageLookupByLibrary.simpleMessage(
      "Disconnected video nodes: ",
    ),
    "diskSpaceInsufficient": MessageLookupByLibrary.simpleMessage(
      "Insufficient disk space. Based on the Steam upload mechanism, adequate space is required on the disk where the application is located. Please free up disk space and try uploading again.",
    ),
    "divide": MessageLookupByLibrary.simpleMessage("Divide"),
    "download": MessageLookupByLibrary.simpleMessage("Download"),
    "downloadFailed": MessageLookupByLibrary.simpleMessage("Download Failed"),
    "downloadSuccess": MessageLookupByLibrary.simpleMessage("Download Success"),
    "downloadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Download from Workshop",
    ),
    "downloading": MessageLookupByLibrary.simpleMessage("Downloading"),
    "downloadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Downloading, please wait",
    ),
    "downloads": MessageLookupByLibrary.simpleMessage("Downloads"),
    "duration": MessageLookupByLibrary.simpleMessage("Total Duration"),
    "editGlobalValue": MessageLookupByLibrary.simpleMessage(
      "Edit Global Value",
    ),
    "editGlobalValueTitle": MessageLookupByLibrary.simpleMessage(
      "Edit Global Value: ",
    ),
    "elementId": MessageLookupByLibrary.simpleMessage("ID"),
    "elementParams": MessageLookupByLibrary.simpleMessage("Element Parameters"),
    "elementText": MessageLookupByLibrary.simpleMessage("Text"),
    "elementType": MessageLookupByLibrary.simpleMessage("Type"),
    "elevation": MessageLookupByLibrary.simpleMessage("Elevation"),
    "enableFlowchartCheck": MessageLookupByLibrary.simpleMessage(
      "Enable Flowchart Check",
    ),
    "enableVideoClickPause": MessageLookupByLibrary.simpleMessage(
      "Enable click to pause video",
    ),
    "enableVideoClickPauseDesc": MessageLookupByLibrary.simpleMessage(
      "When enabled, clicking the video area will toggle play/pause state",
    ),
    "endTime": MessageLookupByLibrary.simpleMessage("End Time"),
    "endTimeLabel": MessageLookupByLibrary.simpleMessage("End Time"),
    "endTimeNotExceedTotal": MessageLookupByLibrary.simpleMessage(
      "End time cannot exceed the total video length",
    ),
    "enterProjectName": MessageLookupByLibrary.simpleMessage(
      "Enter Project Name",
    ),
    "equalTo": MessageLookupByLibrary.simpleMessage("Equal To"),
    "exit": MessageLookupByLibrary.simpleMessage("Exit"),
    "exitFullscreen": MessageLookupByLibrary.simpleMessage("Exit Fullscreen"),
    "failedToGetWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "Failed to get workshop items",
    ),
    "favoriteFailed": MessageLookupByLibrary.simpleMessage("Favorite failed"),
    "favoriteSuccess": MessageLookupByLibrary.simpleMessage("Favorite success"),
    "fileNotFound": MessageLookupByLibrary.simpleMessage("File Not Found"),
    "fileNotFoundTitle": MessageLookupByLibrary.simpleMessage("File not found"),
    "fileNotFoundWithPath": MessageLookupByLibrary.simpleMessage(
      "File not found: ",
    ),
    "filePreparingPleaseRetry": MessageLookupByLibrary.simpleMessage(
      "File preparing, please try again later",
    ),
    "flowChart": MessageLookupByLibrary.simpleMessage("Flow Chart"),
    "flowchart": MessageLookupByLibrary.simpleMessage("Flowchart"),
    "flowchartCheckDescription": MessageLookupByLibrary.simpleMessage(
      "Detect disconnected nodes and start point connections",
    ),
    "flowchartFileNotFound": MessageLookupByLibrary.simpleMessage(
      "FLOWCHART.json file not found",
    ),
    "flowchartLoadError": MessageLookupByLibrary.simpleMessage(
      "Error loading flowchart: ",
    ),
    "flowchartMissingStart": MessageLookupByLibrary.simpleMessage(
      "Flowchart missing start element",
    ),
    "friends": MessageLookupByLibrary.simpleMessage("Friends"),
    "friendsonly": MessageLookupByLibrary.simpleMessage("Friends Only"),
    "fullVideoPath": MessageLookupByLibrary.simpleMessage("Full video path: "),
    "fullscreen": MessageLookupByLibrary.simpleMessage("Fullscreen Mode"),
    "gallery": MessageLookupByLibrary.simpleMessage("Gallery"),
    "gameTitle": MessageLookupByLibrary.simpleMessage(
      "Interactive Video Engine",
    ),
    "gameWindow": MessageLookupByLibrary.simpleMessage("Game Window"),
    "games": MessageLookupByLibrary.simpleMessage("Games"),
    "generalBranchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Branch settings and flowchart saved",
    ),
    "generalSettings": MessageLookupByLibrary.simpleMessage("General Settings"),
    "globalValueName": MessageLookupByLibrary.simpleMessage("Value Name"),
    "globalValueNameHint": MessageLookupByLibrary.simpleMessage(
      "Example: Coins, Health, etc.",
    ),
    "globalValues": MessageLookupByLibrary.simpleMessage("Global Values"),
    "greaterThan": MessageLookupByLibrary.simpleMessage("Greater Than"),
    "greaterThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Greater Than or Equal",
    ),
    "hexagonNode": MessageLookupByLibrary.simpleMessage("Hexagon Node"),
    "hideController": MessageLookupByLibrary.simpleMessage("Hide Controller"),
    "home": MessageLookupByLibrary.simpleMessage("Home"),
    "hour": MessageLookupByLibrary.simpleMessage("Hour"),
    "ilpDesc": MessageLookupByLibrary.simpleMessage("Description"),
    "ilpEditor": MessageLookupByLibrary.simpleMessage("Game Editor"),
    "imageNode": MessageLookupByLibrary.simpleMessage("Image Node"),
    "importImage": MessageLookupByLibrary.simpleMessage("Import Image"),
    "initialValue": MessageLookupByLibrary.simpleMessage("Initial Value"),
    "inputDescription": MessageLookupByLibrary.simpleMessage(
      "Input description",
    ),
    "interfaceLanguage": MessageLookupByLibrary.simpleMessage(
      "Interface language",
    ),
    "invalidEndTime": MessageLookupByLibrary.simpleMessage("Invalid End Time"),
    "invalidFileType": MessageLookupByLibrary.simpleMessage(
      "Invalid File Type",
    ),
    "invalidProjectPath": MessageLookupByLibrary.simpleMessage(
      "Invalid project path",
    ),
    "invalidStartTime": MessageLookupByLibrary.simpleMessage(
      "Invalid Start Time",
    ),
    "invalidTimeFormat": MessageLookupByLibrary.simpleMessage(
      "Invalid time format",
    ),
    "itemNotSubscribed": MessageLookupByLibrary.simpleMessage(
      "Item not subscribed, it will start downloading automatically after subscription",
    ),
    "joinDiscord": MessageLookupByLibrary.simpleMessage(
      "Join Discord to discussion",
    ),
    "language": MessageLookupByLibrary.simpleMessage("Language"),
    "languageSettings": MessageLookupByLibrary.simpleMessage(
      "Language Settings",
    ),
    "lastUpdate": MessageLookupByLibrary.simpleMessage("Last Update"),
    "lessThan": MessageLookupByLibrary.simpleMessage("Less Than"),
    "lessThanOrEqual": MessageLookupByLibrary.simpleMessage(
      "Less Than or Equal",
    ),
    "likeFailed": MessageLookupByLibrary.simpleMessage("Like failed"),
    "likeSuccess": MessageLookupByLibrary.simpleMessage("Like success"),
    "loadArchive": MessageLookupByLibrary.simpleMessage("Load Archive"),
    "loadArchiveError": MessageLookupByLibrary.simpleMessage(
      "Error loading archive: ",
    ),
    "loadFlowchart": MessageLookupByLibrary.simpleMessage("Load Flowchart"),
    "loadingFailed": MessageLookupByLibrary.simpleMessage("Loading Failed"),
    "loadingFailedRetry": MessageLookupByLibrary.simpleMessage(
      "Loading Failed, Click to Retry",
    ),
    "mainWindowFullscreen": MessageLookupByLibrary.simpleMessage(
      "Main Window Fullscreen",
    ),
    "maxValue": MessageLookupByLibrary.simpleMessage("Max Value"),
    "messages": MessageLookupByLibrary.simpleMessage("Messages"),
    "minValue": MessageLookupByLibrary.simpleMessage("Min Value"),
    "minute": MessageLookupByLibrary.simpleMessage("Minute"),
    "modifyChapterTitle": MessageLookupByLibrary.simpleMessage(
      "Modify Chapter Title",
    ),
    "modifyTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Modify Time and Cover",
    ),
    "multiply": MessageLookupByLibrary.simpleMessage("Multiply"),
    "myGames": MessageLookupByLibrary.simpleMessage("My Games"),
    "nameBranchSettingsSaved": MessageLookupByLibrary.simpleMessage(
      "Branch \"\" conditions and value changes saved",
    ),
    "newProject": MessageLookupByLibrary.simpleMessage("New Project"),
    "newWindowFullScreen": MessageLookupByLibrary.simpleMessage(
      "New Window Fullscreen",
    ),
    "newWindowFullScreenDescription": MessageLookupByLibrary.simpleMessage(
      "When opening a new window, make it fullscreen automatically",
    ),
    "news": MessageLookupByLibrary.simpleMessage("News"),
    "no": MessageLookupByLibrary.simpleMessage("No"),
    "noArchivesFound": MessageLookupByLibrary.simpleMessage(
      "No archives found",
    ),
    "noAudioTrack": MessageLookupByLibrary.simpleMessage("No AudioTrack"),
    "noBranchesToSet": MessageLookupByLibrary.simpleMessage(
      "No branches to set",
    ),
    "noGlobalValuesFoundAddFirst": MessageLookupByLibrary.simpleMessage(
      "No global values found, please add values in global value management first",
    ),
    "noSubtitle": MessageLookupByLibrary.simpleMessage("No Subtitle"),
    "noValidFilesFound": MessageLookupByLibrary.simpleMessage(
      "No valid files found for upload, please ensure your project includes supported files only",
    ),
    "noWorkshopItems": MessageLookupByLibrary.simpleMessage(
      "You have no workshop items to update",
    ),
    "nodeDetails": MessageLookupByLibrary.simpleMessage("Node Details"),
    "nodeMarkedAsWatched": MessageLookupByLibrary.simpleMessage(
      "Node marked as watched: ",
    ),
    "normalBranch": MessageLookupByLibrary.simpleMessage("Normal Branch"),
    "notEqual": MessageLookupByLibrary.simpleMessage("Not Equal"),
    "numberType": MessageLookupByLibrary.simpleMessage("Number"),
    "opaque": MessageLookupByLibrary.simpleMessage("Opaque"),
    "openFlowChartFailed": MessageLookupByLibrary.simpleMessage(
      "Failed to open flow chart: ",
    ),
    "openGame": MessageLookupByLibrary.simpleMessage("Open Game"),
    "openGameError": MessageLookupByLibrary.simpleMessage(
      "Error opening game: ",
    ),
    "openGameInNewWindow": MessageLookupByLibrary.simpleMessage(
      "Open Game in New Window",
    ),
    "openGameInNewWindowDesc": MessageLookupByLibrary.simpleMessage(
      "When enabled, open game projects in a new window, otherwise open them in the current window",
    ),
    "openGameTitle": MessageLookupByLibrary.simpleMessage("Open Game"),
    "ovalNode": MessageLookupByLibrary.simpleMessage("Oval Node"),
    "parallelogramNode": MessageLookupByLibrary.simpleMessage(
      "Parallelogram Node",
    ),
    "pause": MessageLookupByLibrary.simpleMessage("Pause"),
    "play": MessageLookupByLibrary.simpleMessage("Play"),
    "playPause": MessageLookupByLibrary.simpleMessage("Play/Pause"),
    "playTimeSettings": MessageLookupByLibrary.simpleMessage(
      "Play Start and End Time Settings",
    ),
    "playbackControl": MessageLookupByLibrary.simpleMessage("Playback Control"),
    "playbackProgress": MessageLookupByLibrary.simpleMessage(
      "Playback Progress",
    ),
    "playbackRate": MessageLookupByLibrary.simpleMessage("Playback Rate"),
    "playbackSpeed": MessageLookupByLibrary.simpleMessage("Playback Speed"),
    "playbackStatus": MessageLookupByLibrary.simpleMessage("Playback Status"),
    "player": MessageLookupByLibrary.simpleMessage("Player"),
    "pleaseEnterTrueOrFalse": MessageLookupByLibrary.simpleMessage(
      "Please enter true or false",
    ),
    "pleaseEnterValidNumber": MessageLookupByLibrary.simpleMessage(
      "Please enter a valid number",
    ),
    "pleaseEnterValue": MessageLookupByLibrary.simpleMessage(
      "Please enter a value",
    ),
    "pleaseEnterValueName": MessageLookupByLibrary.simpleMessage(
      "Please enter a value name",
    ),
    "pleaseEnterVariableName": MessageLookupByLibrary.simpleMessage(
      "Please enter variable name",
    ),
    "pleaseSelectProject": MessageLookupByLibrary.simpleMessage(
      "Please select a project first",
    ),
    "popularGames": MessageLookupByLibrary.simpleMessage("Popular Games"),
    "preparingConfig": MessageLookupByLibrary.simpleMessage("Preparing config"),
    "preparingProjectFiles": MessageLookupByLibrary.simpleMessage(
      "Preparing project files...",
    ),
    "previewImage": MessageLookupByLibrary.simpleMessage("Preview Image"),
    "previewImageDefault": MessageLookupByLibrary.simpleMessage("Default"),
    "private": MessageLookupByLibrary.simpleMessage("Private"),
    "profile": MessageLookupByLibrary.simpleMessage("Profile"),
    "projectCreated": MessageLookupByLibrary.simpleMessage(
      "Project Created Successfully",
    ),
    "projectExistsContent": MessageLookupByLibrary.simpleMessage(
      "This project already exists, do you want to enter this project?",
    ),
    "projectExistsTitle": MessageLookupByLibrary.simpleMessage(
      "Project Exists",
    ),
    "projectLoaded": MessageLookupByLibrary.simpleMessage("Project loaded: "),
    "projectNameHint": MessageLookupByLibrary.simpleMessage("Project Name"),
    "projectNotFound": MessageLookupByLibrary.simpleMessage(
      "Project not found: ",
    ),
    "public": MessageLookupByLibrary.simpleMessage("Public"),
    "publicMode": MessageLookupByLibrary.simpleMessage(
      "Public Mode/Live Streaming Mode",
    ),
    "publicModeDesc": MessageLookupByLibrary.simpleMessage(
      "When enabled, the workshop interface will force the use of \'Everyone\' content rating and not show NSFW tags",
    ),
    "publish": MessageLookupByLibrary.simpleMessage("Publish"),
    "publishTime": MessageLookupByLibrary.simpleMessage("Publish Time"),
    "puzzleHint": MessageLookupByLibrary.simpleMessage(
      "Already reduced one wrong answer",
    ),
    "qteBranch": MessageLookupByLibrary.simpleMessage("QTE Branch"),
    "qteButtonDisplayTime": MessageLookupByLibrary.simpleMessage("QTE\n"),
    "qteButtonDurationSeconds": MessageLookupByLibrary.simpleMessage(
      "Button Display Time: ",
    ),
    "qteButtonPosition": MessageLookupByLibrary.simpleMessage(
      "QTE Button Position",
    ),
    "qteDuration": MessageLookupByLibrary.simpleMessage("QTE Button Duration"),
    "qteDurationDescription": MessageLookupByLibrary.simpleMessage(
      "Time for player to react",
    ),
    "qteFailBranch": MessageLookupByLibrary.simpleMessage("QTE Fail Branch"),
    "qteFailLabel": MessageLookupByLibrary.simpleMessage("Fail"),
    "qtePositionInfo": MessageLookupByLibrary.simpleMessage(
      "QTE Button Position: ",
    ),
    "qteSuccessBranch": MessageLookupByLibrary.simpleMessage(
      "QTE Success Branch",
    ),
    "qteSuccessLabel": MessageLookupByLibrary.simpleMessage("Success"),
    "questionDescription": MessageLookupByLibrary.simpleMessage(
      "Question Description",
    ),
    "range": MessageLookupByLibrary.simpleMessage("Range"),
    "rate": MessageLookupByLibrary.simpleMessage("Playback Rate"),
    "recentlyEdited": MessageLookupByLibrary.simpleMessage("Recently Edited"),
    "recentlyPlayed": MessageLookupByLibrary.simpleMessage("Recently Played"),
    "rectangleNode": MessageLookupByLibrary.simpleMessage("Rectangle Node"),
    "rectangular": MessageLookupByLibrary.simpleMessage("Rectangular"),
    "remainingTimePrefix": MessageLookupByLibrary.simpleMessage(
      "Remaining Time: ",
    ),
    "remainingTimeSuffix": MessageLookupByLibrary.simpleMessage(" seconds"),
    "remoteReservedWord": MessageLookupByLibrary.simpleMessage(
      "\'remote\' is a reserved word, please use a different project name",
    ),
    "removeAll": MessageLookupByLibrary.simpleMessage("Remove All"),
    "removeAllConnections": MessageLookupByLibrary.simpleMessage(
      "Remove All Connections",
    ),
    "removeImage": MessageLookupByLibrary.simpleMessage("Remove Image"),
    "required": MessageLookupByLibrary.simpleMessage("Required"),
    "resume": MessageLookupByLibrary.simpleMessage("Resume"),
    "retry": MessageLookupByLibrary.simpleMessage("Retry"),
    "retryLoading": MessageLookupByLibrary.simpleMessage("Click to Retry"),
    "save": MessageLookupByLibrary.simpleMessage("Save"),
    "saveFailed": MessageLookupByLibrary.simpleMessage(
      "Save failed, please check logs",
    ),
    "saveFlowchart": MessageLookupByLibrary.simpleMessage("Save Flowchart"),
    "saveFlowchartFailed": MessageLookupByLibrary.simpleMessage(
      "Save flowchart failed",
    ),
    "saveSettings": MessageLookupByLibrary.simpleMessage("Save settings"),
    "saving": MessageLookupByLibrary.simpleMessage("Saving..."),
    "savingBranchSettings": MessageLookupByLibrary.simpleMessage(
      "Saving branch settings...",
    ),
    "screenPreview": MessageLookupByLibrary.simpleMessage("Screen Preview"),
    "screenshots": MessageLookupByLibrary.simpleMessage("Screenshots"),
    "search": MessageLookupByLibrary.simpleMessage("Search"),
    "second": MessageLookupByLibrary.simpleMessage("Second"),
    "seconds": MessageLookupByLibrary.simpleMessage("seconds"),
    "seed": MessageLookupByLibrary.simpleMessage("Seed"),
    "segmented": MessageLookupByLibrary.simpleMessage("Segmented"),
    "selectBranchToSet": MessageLookupByLibrary.simpleMessage(
      "Select Branch to Set",
    ),
    "selectContentRating": MessageLookupByLibrary.simpleMessage(
      "Select Content Rating",
    ),
    "selectWorkshopItemToUpdate": MessageLookupByLibrary.simpleMessage(
      "Select Workshop Item to Update",
    ),
    "setAndEnableConditions": MessageLookupByLibrary.simpleMessage(
      "Set and Enable Conditions for Current Option",
    ),
    "setAndEnableValueChanges": MessageLookupByLibrary.simpleMessage(
      "Set and Enable Value Changes After Selecting Option",
    ),
    "setBranchConditionsAndChanges": MessageLookupByLibrary.simpleMessage(
      "Set Branch \"\" Conditions and Value Changes",
    ),
    "setBranchParams": MessageLookupByLibrary.simpleMessage(
      "Set Branch Type and Parameters",
    ),
    "setCurrentTime": MessageLookupByLibrary.simpleMessage("Set Current Time"),
    "setOptionsAndValueChanges": MessageLookupByLibrary.simpleMessage(
      "Set Options and Value Changes",
    ),
    "setTo": MessageLookupByLibrary.simpleMessage("Set To"),
    "settings": MessageLookupByLibrary.simpleMessage("Settings"),
    "settingsSaved": MessageLookupByLibrary.simpleMessage("Settings saved"),
    "shape": MessageLookupByLibrary.simpleMessage("Shape"),
    "showController": MessageLookupByLibrary.simpleMessage("Show Controller"),
    "showControllerDuringPlayback": MessageLookupByLibrary.simpleMessage(
      "Show controller during video playback",
    ),
    "showVideoController": MessageLookupByLibrary.simpleMessage(
      "Show video controller",
    ),
    "showVideoControls": MessageLookupByLibrary.simpleMessage(
      "Show video controls",
    ),
    "showVideoControlsDescription": MessageLookupByLibrary.simpleMessage(
      "Show controller when playing video",
    ),
    "sort": MessageLookupByLibrary.simpleMessage("Sort"),
    "sortBy": MessageLookupByLibrary.simpleMessage("Sort By"),
    "sortByFavorites": MessageLookupByLibrary.simpleMessage("By Favorites"),
    "sortByPublishDate": MessageLookupByLibrary.simpleMessage(
      "By Publish Date",
    ),
    "sortBySubscribers": MessageLookupByLibrary.simpleMessage("By Subscribers"),
    "sortByUpdateDate": MessageLookupByLibrary.simpleMessage("By Update Date"),
    "sortByVote": MessageLookupByLibrary.simpleMessage("By Votes"),
    "startElementIdNotFound": MessageLookupByLibrary.simpleMessage(
      "Cannot get project\'s start element ID",
    ),
    "startGame": MessageLookupByLibrary.simpleMessage("START GAME"),
    "startNode": MessageLookupByLibrary.simpleMessage("Start Node"),
    "startNodeVideoCount": MessageLookupByLibrary.simpleMessage(
      "Start node should connect to 1 video, current: ",
    ),
    "startPoint": MessageLookupByLibrary.simpleMessage("Start"),
    "startTime": MessageLookupByLibrary.simpleMessage("Start Time"),
    "startTimeBeforeEndTime": MessageLookupByLibrary.simpleMessage(
      "Start time must be earlier than end time",
    ),
    "startTimeLabel": MessageLookupByLibrary.simpleMessage("Start Time"),
    "steamAuthorOtherFiles": MessageLookupByLibrary.simpleMessage(
      "Author\'s Other Files",
    ),
    "steamChallenge": MessageLookupByLibrary.simpleMessage("Start challenge"),
    "steamGallery": MessageLookupByLibrary.simpleMessage(
      "Steam workshop gallery",
    ),
    "steamLimitedAccount": MessageLookupByLibrary.simpleMessage(
      "Steam Limited Account",
    ),
    "steamWorkshop": MessageLookupByLibrary.simpleMessage("Steam Workshop"),
    "stop": MessageLookupByLibrary.simpleMessage("Stop"),
    "storageNode": MessageLookupByLibrary.simpleMessage("Storage Node"),
    "style": MessageLookupByLibrary.simpleMessage("Style"),
    "subscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Subscribe failed: ",
    ),
    "subscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "Subscribe success, downloading...",
    ),
    "subtitle": MessageLookupByLibrary.simpleMessage("Subtitle"),
    "subtract": MessageLookupByLibrary.simpleMessage("Subtract"),
    "switchHorizontal": MessageLookupByLibrary.simpleMessage(
      "Switch to horizontal layout",
    ),
    "switchVertical": MessageLookupByLibrary.simpleMessage(
      "Switch to vertical layout",
    ),
    "textType": MessageLookupByLibrary.simpleMessage("Text"),
    "thickness": MessageLookupByLibrary.simpleMessage("Thickness"),
    "timeEdit": MessageLookupByLibrary.simpleMessage("Time Edit"),
    "timeLimit": MessageLookupByLibrary.simpleMessage("Time Limit (seconds)"),
    "timedBranch": MessageLookupByLibrary.simpleMessage("Timed Branch"),
    "title": MessageLookupByLibrary.simpleMessage("Title"),
    "titleCannotBeEmpty": MessageLookupByLibrary.simpleMessage(
      "Title cannot be empty",
    ),
    "titlePositionMiddle": MessageLookupByLibrary.simpleMessage("%, Vertical "),
    "titlePositionPrefix": MessageLookupByLibrary.simpleMessage(
      "Title Position: Horizontal ",
    ),
    "titlePositionSuffix": MessageLookupByLibrary.simpleMessage("%"),
    "to": MessageLookupByLibrary.simpleMessage("to"),
    "toggleConnectable": MessageLookupByLibrary.simpleMessage(
      "Toggle Connectable",
    ),
    "toggleResizable": MessageLookupByLibrary.simpleMessage("Toggle Resizable"),
    "toggleSortDirection": MessageLookupByLibrary.simpleMessage(
      "Toggle Sort Direction",
    ),
    "transparent": MessageLookupByLibrary.simpleMessage("Transparent"),
    "unknownNodeType": MessageLookupByLibrary.simpleMessage("Unknown Type"),
    "unsubscribe": MessageLookupByLibrary.simpleMessage("Unsubscribe"),
    "unsubscribeFailed": MessageLookupByLibrary.simpleMessage(
      "Unsubscribe failed",
    ),
    "unsubscribeSuccess": MessageLookupByLibrary.simpleMessage(
      "Unsubscribe success",
    ),
    "unsupportedFileFormat": MessageLookupByLibrary.simpleMessage(
      "Unsupported file format, workshop only allows video, jpg and json files",
    ),
    "updateSuccess": MessageLookupByLibrary.simpleMessage("Update successful!"),
    "updateTime": MessageLookupByLibrary.simpleMessage("Update Time"),
    "updateWorkshop": MessageLookupByLibrary.simpleMessage(
      "Update Workshop Item",
    ),
    "updateWorkshopError": MessageLookupByLibrary.simpleMessage(
      "Error updating workshop item: ",
    ),
    "updating": MessageLookupByLibrary.simpleMessage("Updating..."),
    "upload": MessageLookupByLibrary.simpleMessage("Upload"),
    "uploadFailed": MessageLookupByLibrary.simpleMessage("Upload Failed"),
    "uploadFailedWithColon": MessageLookupByLibrary.simpleMessage(
      "Upload failed: ",
    ),
    "uploadNow": MessageLookupByLibrary.simpleMessage("Upload Now"),
    "uploadSuccess": MessageLookupByLibrary.simpleMessage("Upload Success"),
    "uploadWorkshop": MessageLookupByLibrary.simpleMessage(
      "Upload to Workshop",
    ),
    "uploading": MessageLookupByLibrary.simpleMessage("Uploading"),
    "uploadingContent": MessageLookupByLibrary.simpleMessage(
      "Uploading content",
    ),
    "uploadingPleaseWait": MessageLookupByLibrary.simpleMessage(
      "Uploading, please wait...",
    ),
    "uploadingPreviewImage": MessageLookupByLibrary.simpleMessage(
      "Uploading preview image",
    ),
    "useNewWindowForEditing": MessageLookupByLibrary.simpleMessage(
      "Open Project in New Window",
    ),
    "useNewWindowForEditingDescription": MessageLookupByLibrary.simpleMessage(
      "When enabled, project editor will open in a new window, otherwise it will open in the current window",
    ),
    "valueInputHint": MessageLookupByLibrary.simpleMessage(
      "Please enter initial value",
    ),
    "valueLabel": MessageLookupByLibrary.simpleMessage("Value"),
    "variableAlreadyExists": MessageLookupByLibrary.simpleMessage(
      "Variable already exists",
    ),
    "variableName": MessageLookupByLibrary.simpleMessage("Variable Name"),
    "variableType": MessageLookupByLibrary.simpleMessage("Type"),
    "version": MessageLookupByLibrary.simpleMessage("Version"),
    "verticalLayoutDescription": MessageLookupByLibrary.simpleMessage(
      "Vertical video editing interface (recommended for high resolution screens)",
    ),
    "videoCover": MessageLookupByLibrary.simpleMessage("Video Cover"),
    "videoFileNotExist": MessageLookupByLibrary.simpleMessage(
      "Video file does not exist: ",
    ),
    "videoNode": MessageLookupByLibrary.simpleMessage("Video Node"),
    "videoNodeLocked": MessageLookupByLibrary.simpleMessage(
      "This video node has not been unlocked yet. Please watch the previous video first",
    ),
    "videoPlayback": MessageLookupByLibrary.simpleMessage("Video Playback"),
    "videoPlaybackError": MessageLookupByLibrary.simpleMessage(
      "Video playback error: ",
    ),
    "videoTimeAndCover": MessageLookupByLibrary.simpleMessage(
      "Video Time and Cover",
    ),
    "viewFlowChart": MessageLookupByLibrary.simpleMessage("View Flow Chart"),
    "viewFlowchart": MessageLookupByLibrary.simpleMessage("View Flowchart"),
    "visibility": MessageLookupByLibrary.simpleMessage("Visibility"),
    "volume": MessageLookupByLibrary.simpleMessage("Volume Level"),
    "volumeControl": MessageLookupByLibrary.simpleMessage("Volume Control"),
    "watchedNodesCount": MessageLookupByLibrary.simpleMessage(
      "Watched Nodes Count",
    ),
    "workshop": MessageLookupByLibrary.simpleMessage("Workshop"),
    "workshopItemUpdated": MessageLookupByLibrary.simpleMessage(
      "Workshop item updated",
    ),
    "workshopItemUploaded": MessageLookupByLibrary.simpleMessage(
      "Workshop item uploaded",
    ),
    "workshopItems": MessageLookupByLibrary.simpleMessage("Workshop Items"),
    "workshopRecommendedDescription": MessageLookupByLibrary.simpleMessage(
      "Written by the village chief of this engine, villagers all approve, this guide will automatically evolve with version updates",
    ),
    "workshopRecommendedTitle": MessageLookupByLibrary.simpleMessage(
      "World Saving Guide",
    ),
    "workshopRuleNoAds": MessageLookupByLibrary.simpleMessage(
      "No advertisements",
    ),
    "workshopRuleNoAdult": MessageLookupByLibrary.simpleMessage(
      "No photographic or realistic pornographic or nude content",
    ),
    "workshopRuleNoCopyright": MessageLookupByLibrary.simpleMessage(
      "No copyright infringement",
    ),
    "workshopRuleNoMisleading": MessageLookupByLibrary.simpleMessage(
      "No misleading preview images",
    ),
    "workshopRuleNoOffensive": MessageLookupByLibrary.simpleMessage(
      "No offensive content or violent, bloody content",
    ),
    "workshopRules": MessageLookupByLibrary.simpleMessage("Workshop Rules"),
    "workshopRulesDescription": MessageLookupByLibrary.simpleMessage(
      "Before submitting interactive videos to the creative workshop, please ensure that they do not violate Steam\'s terms of service, otherwise the interactive videos will be removed:",
    ),
    "workshopRulesSpecial": MessageLookupByLibrary.simpleMessage(
      "Especially interactive videos, preview images and descriptions should follow these rules:",
    ),
  };
}
