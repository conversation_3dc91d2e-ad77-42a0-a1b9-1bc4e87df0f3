import 'package:flutter/material.dart';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:ve/generated/l10n.dart';
import 'dart:convert';
import 'dart:io';
import 'hooks_mobile.dart'  // 导入saveDashboard函数
    if (dart.library.js) 'hooks_web.dart';
import '../../../UI/Views/create/create_game_view.dart'; // 导入GlobalVariable类
import '../../../utils/value_change_helper.dart';
import '../../../UI/shared/value_change_indicator.dart';

/// 数值变化操作符枚举
enum ValueOperator {
  add,       // 加上
  subtract,  // 减去
  multiply,  // 乘以
  divide,    // 除以
  setTo      // 设为
}

/// 条件操作符枚举
enum ConditionOperator {
  greaterThan,        // 大于
  lessThan,           // 小于
  equalTo,            // 等于
  greaterThanOrEqual, // 大于等于
  lessThanOrEqual,    // 小于等于
  notEqual,           // 不等于
  range               // 范围
}

/// 选项及数值变化设置菜单
class GlobalValueSettingsMenu extends StatelessWidget {
  const GlobalValueSettingsMenu({
    required this.element,
    required this.dashboard,
    super.key,
  });

  final FlowElement element;
  final Dashboard dashboard;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        // 使用对话框方式显示设置
        _showBranchSelectionDialog(context);
      },
      child: Text(S.of(context).setOptionsAndValueChanges),
    );
  }

  // 加载全局变量
  Future<List<GlobalVariable>> _loadGlobalVariables() async {
    List<GlobalVariable> variables = [];
    try {
      final savePath = dashboard.projectPath;
      
      // 从FLOWCHART.json文件中读取全局变量
      final flowchartFile = File('$savePath/FLOWCHART.json');
      if (await flowchartFile.exists()) {
        final content = await flowchartFile.readAsString();
        final Map<String, dynamic> flowchartData = jsonDecode(content) as Map<String, dynamic>;
        
        if (flowchartData.containsKey('globalVariables')) {
          final jsonList = flowchartData['globalVariables'] as List;
          variables = jsonList.map((e) => GlobalVariable.fromJson(e)).toList();
          debugPrint('从FLOWCHART.json加载了 ${variables.length} 个全局数值');
        }
      }
    } catch (e) {
      debugPrint('加载全局数值出错: $e');
    }
    return variables;
  }

  // 显示分支选择对话框
  void _showBranchSelectionDialog(BuildContext context) {
    // 保存本地化字符串的引用，避免在widget deactivate后使用context
    final S localizations = S.of(context);

    // 判断是否有分支
    if (element.next.isEmpty || element.next.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.noBranchesToSet))
      );
      return;
    }

    // 显示分支选择对话框
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(S.of(context).selectBranchToSet),
          content: SizedBox(
            width: 400,
            height: 300,
            child: ListView.builder(
              itemCount: element.next.length,
              itemBuilder: (context, index) {
                final connection = element.next[index];
                final destId = connection.destElementId;
                final branchText = _getDestElementText(destId);
                
                return Card(
                  margin: EdgeInsets.symmetric(vertical: 4),
                  child: ListTile(
                    title: Text(S.of(context).branchWithText + branchText),
                    onTap: () {
                      Navigator.of(context).pop();
                      _showGlobalValueSettingsDialog(context, destId);
                    },
                    trailing: Icon(Icons.arrow_forward_ios),
                  ),
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text('取消'),
            ),
          ],
        );
      },
    );
  }

  // 显示选项及数值变化设置对话框
  void _showGlobalValueSettingsDialog(BuildContext context, String branchId) {
    // 加载全局变量并显示对话框
    _loadGlobalVariables().then((globalVariables) {
      // 如果没有全局变量，显示提示
      if (globalVariables.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).noGlobalValuesFoundAddFirst))
        );
        return;
      }

      // 从serializedData读取条件和数值变化设置
      Map<String, dynamic> settings = {};
      if (element.serializedData != null && element.serializedData!.isNotEmpty) {
        try {
          final data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
          // 读取特定分支的设置
          if (data.containsKey(branchId)) {
            settings = Map<String, dynamic>.from(data[branchId]);
          }
        } catch (e) {
          debugPrint('解析选项及数值变化设置出错: $e');
        }
      }

      // 初始化条件设置
      Map<String, Map<String, dynamic>> valueConditions = {};
      for (var variable in globalVariables) {
        String key = variable.name;
        if (settings.containsKey('valueConditions') && 
            (settings['valueConditions'] as Map<String, dynamic>).containsKey(key)) {
          valueConditions[key] = Map<String, dynamic>.from(settings['valueConditions'][key]);
        } else {
          valueConditions[key] = {
            'enabled': false,
            'operator': S.of(context).greaterThan,
            'value': 0
          };
          // 如果操作符是"范围"，添加起始和结束值
          if (valueConditions[key]!["operator"] == S.of(context).range) {
            valueConditions[key]!['rangeStart'] = 0;
            valueConditions[key]!['rangeEnd'] = 0;
          }
        }
      }

      // 初始化数值变化设置
      Map<String, Map<String, dynamic>> valueChanges = {};
      for (var variable in globalVariables) {
        String key = variable.name;
        if (settings.containsKey('valueChanges') && 
            (settings['valueChanges'] as Map<String, dynamic>).containsKey(key)) {
          valueChanges[key] = Map<String, dynamic>.from(settings['valueChanges'][key]);
          
          // 如果是从之前版本加载的数据，将字符串转换为枚举
          if (valueChanges[key]!.containsKey('operator') && valueChanges[key]!['operator'] is String) {
            String opStr = valueChanges[key]!['operator'] as String;
            ValueOperator op = _getValueOperatorFromString(opStr, context);
            // 存储枚举值
            valueChanges[key]!['operatorEnum'] = op;
          }
        } else {
          // 使用枚举初始化
          ValueOperator defaultOp = ValueOperator.add;
          valueChanges[key] = {
            'enabled': false,
            'operator': S.of(context).add,  // 保留本地化字符串用于显示
            'operatorEnum': defaultOp,      // 存储枚举值用于逻辑判断
            'value': 0
          };
        }
      }

      // 操作符选项
      final conditionOperators = [S.of(context).greaterThan, S.of(context).lessThan, S.of(context).equalTo, S.of(context).greaterThanOrEqual, S.of(context).lessThanOrEqual, S.of(context).notEqual, S.of(context).range];
      final changeOperators = [S.of(context).add, S.of(context).subtract, S.of(context).multiply, S.of(context).divide, S.of(context).setTo];

      // 保存函数
      Future<bool> saveSettings() async {
        // 将设置合并到现有serializedData中
        Map<String, dynamic> data = {};
        if (element.serializedData != null && element.serializedData!.isNotEmpty) {
          try {
            data = jsonDecode(element.serializedData!) as Map<String, dynamic>;
          } catch (e) {
            debugPrint('解析已有设置出错: $e');
          }
        }

        // 更新特定分支的条件和数值变化设置
        data[branchId] = {
          'valueConditions': valueConditions,
          'valueChanges': valueChanges
        };

        // 记录保存前的数据状态
        debugPrint('保存分支"$branchId"的设置:');
        debugPrint('条件: ${jsonEncode(valueConditions)}');
        debugPrint('数值变化: ${jsonEncode(valueChanges)}');

        // 将Map转换为JSON字符串，保存到元素的serializedData字段
        element.serializedData = jsonEncode(data);

        // 保存Dashboard
        try {
          // 获取项目保存路径
          final savePath = dashboard.projectPath;
          debugPrint('项目保存路径: $savePath');
          
          // 先备份FLOWCHART.json中的全局变量数据
          Map<String, dynamic>? flowchartData;
          List<dynamic>? globalVarsBackup;
          
          final flowchartFile = File('$savePath/FLOWCHART.json');
          if (await flowchartFile.exists()) {
            try {
              final content = await flowchartFile.readAsString();
              flowchartData = jsonDecode(content) as Map<String, dynamic>;
              
              // 备份全局变量
              if (flowchartData.containsKey('globalVariables')) {
                globalVarsBackup = flowchartData['globalVariables'] as List;
                debugPrint('已备份${globalVarsBackup.length}个全局变量');
              }
            } catch (e) {
              debugPrint('读取流程图文件时出错: $e');
            }
          }
          
          // 保存Dashboard到项目路径
          await saveDashboard(dashboard, Directory(savePath));
          
          // 确保FLOWCHART.json存在并更新全局变量
          if (await flowchartFile.exists()) {
            try {
              // 重新读取保存后的流程图文件
              final content = await flowchartFile.readAsString();
              flowchartData = jsonDecode(content) as Map<String, dynamic>;
              
              // 恢复或更新globalVariables字段
              if (globalVarsBackup != null) {
                // 使用备份的全局变量
                flowchartData['globalVariables'] = globalVarsBackup;
                
                // 保存更新后的流程图文件
                final jsonString = JsonEncoder.withIndent('  ').convert(flowchartData);
                await flowchartFile.writeAsString(jsonString);
                debugPrint('已恢复${globalVarsBackup.length}个全局变量到流程图文件中');
              } else if (!flowchartData.containsKey('globalVariables')) {
                debugPrint('FLOWCHART.json中不包含全局变量，需要添加全局变量');
                
                // 加载全局变量
                final globalVars = await _loadGlobalVariables();
                if (globalVars.isNotEmpty) {
                  flowchartData['globalVariables'] = globalVars.map((e) => e.toJson()).toList();
                  
                  // 保存更新后的流程图文件
                  final jsonString = JsonEncoder.withIndent('  ').convert(flowchartData);
                  await flowchartFile.writeAsString(jsonString);
                  debugPrint('已将全局变量添加到流程图文件中');
                } else {
                  debugPrint('警告：无法找到全局变量数据');
                }
              }
            } catch (e) {
              debugPrint('处理流程图文件时出错: $e');
            }
            
            debugPrint('FLOWCHART.json保存成功: ${await flowchartFile.length()} 字节');
          } else {
            debugPrint('警告: FLOWCHART.json文件未找到，可能保存失败');
          }
          
          debugPrint('Dashboard保存成功，元素ID: ${element.id}');
          
          // 注意：不要在这里直接使用context，因为saveSettings是异步方法，
          // 可能在对话框关闭后执行，此时context已经无效
          
          return true;
        } catch (e) {
          debugPrint('保存Dashboard时出错: $e');
          return false;
        }
      }

      // 获取分支目标元素的文本
      final branchText = _getDestElementText(branchId);

      // 显示对话框
      if (context.mounted) {
        showDialog(
          context: context,
          builder: (context) {
            // 创建用于管理文本字段的控制器映射
            final Map<String, TextEditingController> controllers = {};
            
            // 在对话框关闭时清理控制器资源
            void disposeControllers() {
              for (var controller in controllers.values) {
                controller.dispose();
              }
              controllers.clear();
            }
            
            return StatefulBuilder(
              builder: (context, setState) {
                // 确保在组件卸载时释放资源
                Future.delayed(Duration.zero, () {
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (!context.mounted) {
                      disposeControllers();
                    }
                  });
                });
                
                // 获取或创建控制器
                TextEditingController getController(String key, String field, dynamic defaultValue) {
                  final String controllerKey = '${key}_${field}';
                  if (!controllers.containsKey(controllerKey)) {
                    // 为该字段创建新控制器
                    Map<String, dynamic> data;
                    if (field == 'value' || field == 'rangeStart' || field == 'rangeEnd') {
                      data = valueConditions[key]!;
                    } else if (field == 'change_value') {
                      data = valueChanges[key]!;
                      field = 'value'; // change_value实际上是映射到valueChanges中的value字段
                    } else {
                      data = valueChanges[key]!;
                    }
                    
                    final value = data[field] ?? defaultValue;
                    controllers[controllerKey] = TextEditingController(text: '$value');
                  }
                  return controllers[controllerKey]!;
                }
                
                return AlertDialog(
                  title: Text(S.of(context).setBranchConditionsAndChanges + branchText),
                  content: SizedBox(
                    width: 600,
                    height: 600,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 第一部分：条件设置
                          CheckboxListTile(
                            title: Text(S.of(context).setAndEnableConditions, 
                              style: TextStyle(fontWeight: FontWeight.bold)),
                            value: valueConditions.values.any((condition) => condition['enabled'] == true),
                            onChanged: (value) {
                              setState(() {
                                for (var key in valueConditions.keys) {
                                  valueConditions[key]!['enabled'] = value;
                                }
                              });
                            },
                          ),
                          
                          // 条件列表
                          ...valueConditions.entries.map((entry) {
                            final key = entry.key;
                            final condition = entry.value;
                            
                            // 查找对应的全局变量获取类型信息
                            final variable = globalVariables.firstWhere(
                              (v) => v.name == key,
                              orElse: () => GlobalVariable(name: key, value: 0, type: 'number')
                            );
                            
                            final color = _getVariableTypeColor(variable.type);
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Row(
                                children: [
                                  // 复选框
                                  Checkbox(
                                    value: condition['enabled'] == true,
                                    onChanged: (value) {
                                      setState(() {
                                        condition['enabled'] = value;
                                      });
                                    },
                                  ),
                                  
                                  // 数值名称
                                  SizedBox(
                                    width: 120,
                                    child: Text(key, style: TextStyle(color: color)),
                                  ),
                                  
                                  // 操作符下拉框
                                  SizedBox(
                                    width: 120,
                                    child: DropdownButton<String>(
                                      value: condition['operator'] as String,
                                      isExpanded: true,
                                      onChanged: (value) {
                                        setState(() {
                                          condition['operator'] = value;
                                          // 当切换到"范围"操作符时，初始化范围值
                                          if (value == S.of(context).range) {
                                            condition['rangeStart'] = condition['rangeStart'] ?? 0;
                                            condition['rangeEnd'] = condition['rangeEnd'] ?? 0;
                                          }
                                        });
                                      },
                                      items: conditionOperators.map((op) => 
                                        DropdownMenuItem(value: op, child: Text(op))
                                      ).toList(),
                                    ),
                                  ),
                                  
                                  SizedBox(width: 8),
                                  
                                  // 值输入框
                                  Expanded(
                                    child: condition['operator'] == S.of(context).range 
                                      ? Row(
                                          children: [
                                            Expanded(
                                              child: TextField(
                                                key: ValueKey('range_start_${key}'),
                                                decoration: InputDecoration(
                                                  border: OutlineInputBorder(),
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                                  hintText: S.of(context).minValue,
                                                ),
                                                keyboardType: _getKeyboardType(variable.type),
                                                controller: getController(key, 'rangeStart', 0),
                                                onChanged: (value) {
                                                  // 直接更新数据模型，无需控制器
                                                  setState(() {
                                                    condition['rangeStart'] = _parseValue(value, variable.type);
                                                  });
                                                },
                                              ),
                                            ),
                                            SizedBox(width: 8),
                                            Text(S.of(context).to),
                                            SizedBox(width: 8),
                                            Expanded(
                                              child: TextField(
                                                key: ValueKey('range_end_${key}'),
                                                decoration: InputDecoration(
                                                  border: OutlineInputBorder(),
                                                  contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                                  hintText: S.of(context).maxValue,
                                                ),
                                                keyboardType: _getKeyboardType(variable.type),
                                                controller: getController(key, 'rangeEnd', 0),
                                                onChanged: (value) {
                                                  // 直接更新数据模型，无需控制器
                                                  setState(() {
                                                    condition['rangeEnd'] = _parseValue(value, variable.type);
                                                  });
                                                },
                                              ),
                                            ),
                                          ],
                                        )
                                      : TextField(
                                          key: ValueKey('value_${key}'),
                                          decoration: InputDecoration(
                                            border: OutlineInputBorder(),
                                            contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                          ),
                                          keyboardType: _getKeyboardType(variable.type),
                                          controller: getController(key, 'value', 0),
                                          onChanged: (value) {
                                            // 根据变量类型解析值
                                            condition['value'] = _parseValue(value, variable.type);
                                          },
                                        ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                          
                          SizedBox(height: 20),
                          
                          // 第二部分：数值变化设置
                          CheckboxListTile(
                            title: Text(S.of(context).setAndEnableValueChanges, 
                              style: TextStyle(fontWeight: FontWeight.bold)),
                            value: valueChanges.values.any((change) => change['enabled'] == true),
                            onChanged: (value) {
                              setState(() {
                                for (var key in valueChanges.keys) {
                                  valueChanges[key]!['enabled'] = value;
                                }
                              });
                            },
                          ),
                          
                          // 数值变化列表
                          ...valueChanges.entries.map((entry) {
                            final key = entry.key;
                            final change = entry.value;
                            
                            // 查找对应的全局变量获取类型信息
                            final variable = globalVariables.firstWhere(
                              (v) => v.name == key,
                              orElse: () => GlobalVariable(name: key, value: 0, type: 'number')
                            );
                            
                            final color = _getVariableTypeColor(variable.type);
                            
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 8.0),
                              child: Row(
                                children: [
                                  // 复选框
                                  Checkbox(
                                    value: change['enabled'] == true,
                                    onChanged: (value) {
                                      setState(() {
                                        change['enabled'] = value;
                                      });
                                    },
                                  ),
                                  
                                  // 数值名称
                                  SizedBox(
                                    width: 120,
                                    child: Text(key, style: TextStyle(color: color)),
                                  ),
                                  
                                  // 操作符下拉框
                                  SizedBox(
                                    width: 120,
                                    child: DropdownButton<String>(
                                      value: change['operator'] as String,
                                      isExpanded: true,
                                      onChanged: (value) {
                                        setState(() {
                                          change['operator'] = value;
                                        });
                                      },
                                      items: changeOperators.map((op) => 
                                        DropdownMenuItem(value: op, child: Text(op))
                                      ).toList(),
                                    ),
                                  ),
                                  
                                  SizedBox(width: 8),
                                  
                                  // 值输入框
                                  Expanded(
                                    child: TextField(
                                      key: ValueKey('change_value_${key}'),
                                      decoration: InputDecoration(
                                        border: OutlineInputBorder(),
                                        contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 8),
                                      ),
                                      keyboardType: _getKeyboardType(variable.type),
                                      controller: getController(key, 'change_value', 0),
                                      onChanged: (value) {
                                        // 根据变量类型解析值
                                        change['value'] = _parseValue(value, variable.type);
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ],
                      ),
                    ),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Text(S.of(context).cancel),
                    ),
                    ElevatedButton(
                      onPressed: () async {
                        // 显示保存中提示
                        final scaffoldMessenger = ScaffoldMessenger.of(context);
                        scaffoldMessenger.showSnackBar(
                          SnackBar(
                            content: Row(
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  strokeWidth: 2,
                                ),
                                SizedBox(width: 20),
                                Text(S.of(context).savingBranchSettings)
                              ],
                            ),
                            duration: Duration(seconds: 1),
                          ),
                        );
                        
                        // 保存前同步所有控制器的值到数据模型
                        for (var key in controllers.keys) {
                          final parts = key.split('_');
                          if (parts.length >= 2) {
                            final varName = parts[0];
                            final fieldType = parts.sublist(1).join('_');
                            
                            final value = controllers[key]!.text;
                            
                            // 根据字段类型决定更新哪个数据结构
                            if (fieldType == 'value') {
                              // 条件值
                              if (valueConditions.containsKey(varName)) {
                                final variable = globalVariables.firstWhere(
                                  (v) => v.name == varName,
                                  orElse: () => GlobalVariable(name: varName, value: 0, type: 'number')
                                );
                                valueConditions[varName]!['value'] = _parseValue(value, variable.type);
                                debugPrint('同步条件值: $varName = ${valueConditions[varName]!['value']}');
                              }
                            } else if (fieldType == 'rangeStart') {
                              // 范围起始值
                              if (valueConditions.containsKey(varName)) {
                                final variable = globalVariables.firstWhere(
                                  (v) => v.name == varName,
                                  orElse: () => GlobalVariable(name: varName, value: 0, type: 'number')
                                );
                                valueConditions[varName]!['rangeStart'] = _parseValue(value, variable.type);
                                debugPrint('同步范围起始值: $varName = ${valueConditions[varName]!['rangeStart']}');
                              }
                            } else if (fieldType == 'rangeEnd') {
                              // 范围结束值
                              if (valueConditions.containsKey(varName)) {
                                final variable = globalVariables.firstWhere(
                                  (v) => v.name == varName,
                                  orElse: () => GlobalVariable(name: varName, value: 0, type: 'number')
                                );
                                valueConditions[varName]!['rangeEnd'] = _parseValue(value, variable.type);
                                debugPrint('同步范围结束值: $varName = ${valueConditions[varName]!['rangeEnd']}');
                              }
                            } else if (fieldType == 'change_value') {
                              // 变化值
                              if (valueChanges.containsKey(varName)) {
                                final variable = globalVariables.firstWhere(
                                  (v) => v.name == varName,
                                  orElse: () => GlobalVariable(name: varName, value: 0, type: 'number')
                                );
                                valueChanges[varName]!['value'] = _parseValue(value, variable.type);
                                debugPrint('同步变化值: $varName = ${valueChanges[varName]!['value']}');
                              }
                            }
                          }
                        }
                        
                        debugPrint('开始保存分支"$branchId"的设置数据');
                        
                        // 记录当前数据状态供调试
                        debugPrint('条件设置: ${jsonEncode(valueConditions)}');
                        debugPrint('数值变化设置: ${jsonEncode(valueChanges)}');
                        
                        final success = await saveSettings();
                        if (success) {
                          // 保存前清理控制器资源
                          disposeControllers();
                          
                          // 先获取要显示的数值变化信息
                          ValueChangeType? changeType;
                          String? changeValueName;
                          
                          // 找出第一个启用的变化
                          for (var entry in valueChanges.entries) {
                            if (entry.value['enabled'] == true) {
                              // 根据操作符判断是增加还是减少
                              ValueOperator operator;
                              
                              // 获取操作符枚举值
                              if (entry.value.containsKey('operatorEnum')) {
                                // 如果已经有枚举值，直接使用
                                operator = entry.value['operatorEnum'] as ValueOperator;
                              } else if (entry.value.containsKey('operator')) {
                                // 向后兼容：如果只有字符串，转换为枚举
                                String opStr = entry.value['operator'] as String;
                                operator = _getValueOperatorFromString(opStr, context);
                              } else {
                                // 默认值
                                operator = ValueOperator.add;
                              }
                              
                              // 使用枚举进行逻辑判断
                              changeType = (operator == ValueOperator.add || operator == ValueOperator.multiply) 
                                  ? ValueChangeType.increase 
                                  : (operator == ValueOperator.subtract || operator == ValueOperator.divide) 
                                      ? ValueChangeType.decrease 
                                      : ValueChangeType.increase;
                              
                              changeValueName = entry.key;
                              // 只获取第一个启用的变化
                              break;
                            }
                          }
                          
                          // 保存当前上下文引用
                          final currentContext = context;
                          // 先关闭对话框
                          Navigator.of(context).pop();
                          
                          // 检查上下文是否仍然有效
                          if (currentContext.mounted) {
                            ScaffoldMessenger.of(currentContext).showSnackBar(
                              SnackBar(content: Text(S.of(context).nameBranchSettingsSaved + branchText)),
                            );
                            
                            // 在对话框关闭后，仅当上下文仍然有效且有变化数据时才显示数值变化提示示例
                            if (changeType != null && changeValueName != null && currentContext.mounted) {
                              try {
                                // 使用安全的方式显示指示器
                                WidgetsBinding.instance.addPostFrameCallback((_) {
                                  if (currentContext.mounted) {
                                    ValueChangeHelper.showIndicator(
                                      currentContext,
                                      type: changeType!,
                                      valueName: changeValueName!,
                                      position: Alignment.topRight,
                                    );
                                  }
                                });
                              } catch (e) {
                                debugPrint('显示数值变化指示器时出错: $e');
                              }
                            }
                          }
                        } else {
                          // 保存失败提示
                          // 在保存失败的情况下使用安全的方式显示错误
                          if (context.mounted) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text(S.of(context).saveFailed),
                                backgroundColor: Colors.red,
                              ),
                            );
                          }
                        }
                      },
                      child: Text(S.of(context).apply),
                    ),
                  ],
                );
              },
            );
          },
        );
      }
    });
  }

  /// 将本地化字符串转换为ValueOperator枚举
  ValueOperator _getValueOperatorFromString(String text, BuildContext context) {
    if (text == S.of(context).add) return ValueOperator.add;
    if (text == S.of(context).subtract) return ValueOperator.subtract;
    if (text == S.of(context).multiply) return ValueOperator.multiply;
    if (text == S.of(context).divide) return ValueOperator.divide;
    if (text == S.of(context).setTo) return ValueOperator.setTo;
    return ValueOperator.add; // 默认值
  }

  /// 将ValueOperator枚举转换为本地化字符串
  String _getStringFromValueOperator(ValueOperator op, BuildContext context) {
    switch (op) {
      case ValueOperator.add: return S.of(context).add;
      case ValueOperator.subtract: return S.of(context).subtract;
      case ValueOperator.multiply: return S.of(context).multiply;
      case ValueOperator.divide: return S.of(context).divide;
      case ValueOperator.setTo: return S.of(context).setTo;
    }
  }

  /// 将本地化字符串转换为ConditionOperator枚举
  ConditionOperator _getConditionOperatorFromString(String text, BuildContext context) {
    if (text == S.of(context).greaterThan) return ConditionOperator.greaterThan;
    if (text == S.of(context).lessThan) return ConditionOperator.lessThan;
    if (text == S.of(context).equalTo) return ConditionOperator.equalTo;
    if (text == S.of(context).greaterThanOrEqual) return ConditionOperator.greaterThanOrEqual;
    if (text == S.of(context).lessThanOrEqual) return ConditionOperator.lessThanOrEqual;
    if (text == S.of(context).notEqual) return ConditionOperator.notEqual;
    if (text == S.of(context).range) return ConditionOperator.range;
    return ConditionOperator.greaterThan; // 默认值
  }

  /// 将ConditionOperator枚举转换为本地化字符串
  String _getStringFromConditionOperator(ConditionOperator op, BuildContext context) {
    switch (op) {
      case ConditionOperator.greaterThan: return S.of(context).greaterThan;
      case ConditionOperator.lessThan: return S.of(context).lessThan;
      case ConditionOperator.equalTo: return S.of(context).equalTo;
      case ConditionOperator.greaterThanOrEqual: return S.of(context).greaterThanOrEqual;
      case ConditionOperator.lessThanOrEqual: return S.of(context).lessThanOrEqual;
      case ConditionOperator.notEqual: return S.of(context).notEqual;
      case ConditionOperator.range: return S.of(context).range;
    }
  }

  // 根据变量类型获取颜色
  Color _getVariableTypeColor(String type) {
    switch (type) {
      case 'number':
        return Colors.green;
      case 'text':
        return Colors.blue;
      case 'boolean':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  // 根据变量类型获取键盘类型
  TextInputType _getKeyboardType(String type) {
    switch (type) {
      case 'number':
        return TextInputType.number;
      case 'text':
        return TextInputType.text;
      case 'boolean':
        return TextInputType.text;
      default:
        return TextInputType.text;
    }
  }

  // 根据变量类型解析值
  dynamic _parseValue(String value, String type) {
    switch (type) {
      case 'number':
        return int.tryParse(value) ?? 0;
      case 'text':
        return value;
      case 'boolean':
        return value.toLowerCase() == 'true';
      default:
        return value;
    }
  }

  // 获取目标元素的文本
  String _getDestElementText(String destId) {
    // 查找目标元素
    try {
      final destElement = dashboard.elements.firstWhere((e) => e.id == destId);
      return destElement.text;
    } catch (e) {
      return destId;
    }
  }
} 