import 'dart:convert';
import 'dart:math' as Math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:media_kit/media_kit.dart';
import 'dart:io';
import '../../../utils/video.dart';
import 'dart:async';
import 'package:flutter_flow_chart/flutter_flow_chart.dart';
import 'package:ve/generated/l10n.dart';
import '../../../utils/workshop_path.dart';
import '../../../utils/data.dart';
import '../../../utils/save_archive.dart';
import '../../../utils/coin_manager.dart';
import '../../../UI/shared/coin_animation.dart';
import 'package:fullscreen_window/fullscreen_window.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:ve/utils/fullscreen_manager.dart';
import '../../../utils/value_change_helper.dart';
import '../../../next-gen-ui/title_screen/settings_screen.dart';

class Element {
  final String path;
  final String startTime;
  final String endTime;
  final int kind;
  final String id;
  final List<String> next;
  final String text;
  final String? branchTitle;
  final double? branchButtonOpacity;
  final Offset? branchButtonPosition;
  final Map<String, String>? branchButtonTexts;
  final Map<String, Offset>? branchButtonPositions;
  final Offset? branchTitlePosition;
  final String? serializedData;
  final bool? buttonTextOnly; // 新增：按钮是否仅显示文字

  Element({
    required this.path,
    required this.startTime,
    required this.endTime,
    required this.kind,
    required this.id,
    required this.next,
    required this.text,
    this.branchTitle,
    this.branchButtonOpacity,
    this.branchButtonPosition,
    this.branchButtonTexts,
    this.branchButtonPositions,
    this.branchTitlePosition,
    this.serializedData,
    this.buttonTextOnly, // 新增字段
  });

  factory Element.fromJson(Map<String, dynamic> json) {
    List<String> nextIds = [];
    if (json['next'] != null) {
      nextIds = (json['next'] as List)
          .map((next) => next['destElementId'].toString())
          .toList();
    }

    // 解析buttonTextOnly字段
    bool? buttonTextOnly;
    if (json['data'] != null && json['data'].toString().isNotEmpty) {
      try {
        final data = jsonDecode(json['data'] as String) as Map<String, dynamic>;
        if (data.containsKey('buttonTextOnly')) {
          buttonTextOnly = data['buttonTextOnly'] as bool;
        }
      } catch (e) {
        debugPrint('解析按钮文字设置出错: $e');
      }
    }

    return Element(
      path: json['path'] ?? '',
      startTime: json['startTime'] ?? '',
      endTime: json['endTime'] ?? '',
      kind: json['kind'] ?? 0,
      id: json['id'] ?? '',
      next: nextIds,
      text: json['text'] ?? '',
      branchTitle: json['branchTitle'] as String?,
      branchButtonOpacity: json['branchButtonOpacity'] as double?,
      branchButtonPosition: json['branchButtonPosition'] != null
          ? Offset(json['branchButtonPosition']['dx'], json['branchButtonPosition']['dy'])
          : null,
      branchButtonTexts: json['branchButtonTexts'] != null
          ? Map<String, String>.fromEntries(
              (json['branchButtonTexts'] as Map<String, dynamic>).entries
                  .map((entry) => MapEntry(entry.key, entry.value.toString()))
            )
          : null,
      branchButtonPositions: json['branchButtonPositions'] != null
          ? Map<String, Offset>.fromEntries(
              (json['branchButtonPositions'] as Map<String, dynamic>).entries
                  .map((entry) => MapEntry(
                    entry.key,
                    Offset(
                      (entry.value as Map<String, dynamic>)['dx'] as double,
                      (entry.value as Map<String, dynamic>)['dy'] as double
                    )
                  ))
            )
          : null,
      branchTitlePosition: json['branchTitlePosition'] != null
          ? Offset(json['branchTitlePosition']['dx'], json['branchTitlePosition']['dy'])
          : null,
      serializedData: json['data'] as String?,
      buttonTextOnly: buttonTextOnly, // 设置解析的值
    );
  }
}

class OpenGameView extends StatefulWidget {
  final String projectPath;
  final String workshopItemId;
  final String projectName;
  final String? initialNodeId; // 添加初始节点ID参数
  
  const OpenGameView({
    Key? key, 
    required this.projectPath, 
    required this.workshopItemId, 
    required this.projectName,
    this.initialNodeId, // 可选参数
  }) : super(key: key);

  @override
  _OpenGameViewState createState() => _OpenGameViewState();
}

class _OpenGameViewState extends State<OpenGameView> with TickerProviderStateMixin {
  // 添加一个控制显示的状态变量
  bool showController = false;
  bool _isFullscreen = false; // 添加全屏状态变量
  late Dashboard dashboard;
  late VideoPlayerManager videoPlayerManager;
  late List<Element> elements;
  late int currentIndex;
  bool showBranchOptions = false;
  bool isPlaying = false;
  StreamSubscription? _positionSubscription;
  // 添加全局项目路径变量
  late String _projectPath;
  late String _projectName;
  // 添加存档相关变量
  String? _startElementId;
  ProjectArchive? _projectArchive;
  Set<String> _watchedNodeIds = {}; // 用于跟踪已观看的节点
  bool _showingCoinAnimation = false; // 是否显示金币动画
  SharedPreferences? _prefs; // 修改为可空类型，不使用late
  late FullscreenManager _fullscreenManager;
  
  // 添加烧绳线动画控制器
  AnimationController? _burnLineController;
  Animation<double>? _burnLineAnimation;
  
  // 添加QTE相关变量
  bool showQteButton = false; // 是否显示QTE按钮
  int qteDuration = 3; // QTE持续时间，默认3秒
  String? qteSuccessBranchId; // QTE成功分支ID
  String? qteFailBranchId; // QTE失败分支ID
  Offset qteButtonPosition = Offset(0.5, 0.5); // QTE按钮位置
  Timer? qteTimer; // QTE计时器
  
  // 快速点击QTE相关变量
  int qteClicksRequired = 3; // 需要点击的次数，从10改为3
  int qteCurrentClicks = 0; // 当前点击次数
  double qteProgress = 0.0; // 进度条进度
  Timer? qteProgressDecayTimer; // 进度条衰减计时器

  // 全局变量管理
  Map<String, dynamic> _globalVariables = {};
  bool _globalVariablesLoaded = false;

  @override
  void initState() {
    super.initState();
    _projectPath = widget.projectPath;
    _projectName = widget.projectName;
    _fullscreenManager = FullscreenManager();
    dashboard = Dashboard(projectPath: _projectPath, projectName: _projectName);
    currentIndex = 0;
    elements = [];
    videoPlayerManager = VideoPlayerManager();
    
    // 设置初始状态为正在播放，避免闪过流程图
    isPlaying = true;
    
    // 加载存档和视频
    _loadArchiveAndVideos();
    
    // 加载设置
    _loadSettings();
    
    // 加载全局变量
    _loadGlobalVariables();
  }
  
  // 加载全局变量
  Future<void> _loadGlobalVariables() async {
    try {
      // 首先尝试从存档中加载全局变量
      if (_projectArchive != null && _projectArchive!.globalVariables != null) {
        _globalVariables = Map<String, dynamic>.from(_projectArchive!.globalVariables!);
        _globalVariablesLoaded = true;
        debugPrint('从存档中加载了 ${_globalVariables.length} 个全局变量');
        return;
      }
      
      // 如果存档中没有全局变量，从流程图FLOWCHART.json中加载初始值
      final flowchartFile = File('${_projectPath}/FLOWCHART.json');
      if (await flowchartFile.exists()) {
        final content = await flowchartFile.readAsString();
        final flowchartData = jsonDecode(content) as Map<String, dynamic>;
        
        // 检查是否包含globalVariables字段
        if (flowchartData.containsKey('globalVariables')) {
          final globalVarsJson = flowchartData['globalVariables'] as List;
          
          // 初始化全局变量Map
          _globalVariables = {};
          for (var item in globalVarsJson) {
            _globalVariables[item['name']] = item['value'];
          }
          
          _globalVariablesLoaded = true;
          debugPrint('从流程图FLOWCHART.json中加载了 ${_globalVariables.length} 个全局变量');
        } else {
          _globalVariables = {};
          debugPrint('FLOWCHART.json中不存在全局变量数据');
        }
      } else {
        _globalVariables = {};
        debugPrint('未找到全局变量文件，初始化空全局变量Map');
      }
    } catch (e) {
      debugPrint('加载全局变量出错: $e');
      _globalVariables = {};
    }
  }
  
  // 获取全局变量值
  dynamic _getGlobalVariableValue(String name) {
    // 如果全局变量还未加载，先加载
    if (!_globalVariablesLoaded) {
      return 0; // 默认返回0
    }
    
    // 返回变量值，如果不存在则返回0
    return _globalVariables[name] ?? 0;
  }
  
  // 更新全局变量值
  void _updateGlobalVariable(String name, dynamic value) {
    _globalVariables[name] = value;
    _saveGlobalVariablesToArchive();
  }
  
  // 将全局变量保存到存档中
  Future<void> _saveGlobalVariablesToArchive() async {
    try {
      if (_projectArchive != null && _startElementId != null) {
        // 更新存档中的全局变量
        _projectArchive = _projectArchive!.updateGlobalVariables(_globalVariables);
        
        // 保存存档
        await SaveArchive.saveArchive(_projectArchive!);
        debugPrint('已将 ${_globalVariables.length} 个全局变量保存到存档中');
      }
    } catch (e) {
      debugPrint('保存全局变量到存档出错: $e');
    }
  }

  /// 加载存档和视频
  Future<void> _loadArchiveAndVideos() async {
    // 先加载流程图元素
    elements = await loadDashboard();
    
    // 查找起始元素
    final startIndex = elements.indexWhere((element) => element.kind == 0);
    if (startIndex == -1) {
      if (mounted && context.mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(S.of(context).flowchartMissingStart)),
        );
      }
      return;
    }
    
    // 保存起始元素ID
    _startElementId = elements[startIndex].id;
    
    // 检查是否有指定的初始节点ID
    if (widget.initialNodeId != null) {
      // 查找指定节点的索引
      final specifiedNodeIndex = elements.indexWhere((element) => element.id == widget.initialNodeId);
      if (specifiedNodeIndex != -1) {
        // 如果找到了指定的节点，直接设置currentIndex并播放该节点
        currentIndex = specifiedNodeIndex;
        
        // 尝试加载存档以获取已观看节点信息
        _projectArchive = await SaveArchive.loadArchive(_startElementId!);
        if (_projectArchive != null) {
          // 加载已观看节点列表
          _watchedNodeIds = _projectArchive!.videoQueue
              .where((node) => node.completed)
              .map((node) => node.nodeId)
              .toSet();
        } else {
          // 如果存档不存在，创建初始存档
          _projectArchive = await SaveArchive.createInitialArchive(
            _startElementId!,
            _projectPath
          );
        }
        
        // 开始播放视频
        if (mounted) {
          setState(() {
            isPlaying = true;
          });
          playVideo();
          
          // 记录最近玩过的项目
          _saveRecentProject();
        }
        return;
      }
    }
    
    // 如果没有指定节点ID或找不到指定节点，使用正常的加载流程
    // 尝试加载存档
    if (_startElementId != null) {
      _projectArchive = await SaveArchive.loadArchive(_startElementId!);
      
      // 如果存档不存在，创建初始存档
      if (_projectArchive == null) {
        print('未找到存档，创建初始存档');
        _projectArchive = await SaveArchive.createInitialArchive(
          _startElementId!,
          _projectPath
        );
      } else {
        print('成功加载存档: ${_projectArchive!.currentNodeId}');
        
        // 加载已观看节点列表
        _watchedNodeIds = _projectArchive!.videoQueue
            .where((node) => node.completed)
            .map((node) => node.nodeId)
            .toSet();
        
        // 设置当前节点为存档中的节点
        final savedNodeIndex = elements.indexWhere(
            (element) => element.id == _projectArchive!.currentNodeId);
        if (savedNodeIndex != -1) {
          currentIndex = savedNodeIndex;
        } else {
          currentIndex = startIndex; // 如果找不到存档的节点，使用起始节点
        }
      }
    } else {
      currentIndex = startIndex;
    }
    
    // 开始播放视频
    if (mounted) {
      setState(() {
        isPlaying = true;
      });
      playVideo();
      
      // 记录最近玩过的项目
      _saveRecentProject();
    }
  }
  
  /// 加载视频（保留原方法以兼容其他调用）
  Future<void> loadAndPlayVideos() async {
    await _loadArchiveAndVideos();
    
    // 记录最近玩过的项目
    _saveRecentProject();
  }

  Widget buildDashboard() {
    // 将已观看节点ID集合传递给Dashboard
    dashboard.setWatchedNodeIds(_watchedNodeIds);
    // 设置为播放模式
    dashboard.setPlayMode(true);
    
    return Container(
      color: Colors.white,
      child: FlowChart(
        dashboard: dashboard,
        onElementPressed: (context, position, element) {
          // 只有已观看的节点或起始节点才能点击播放
          final bool isNodeWatched = _watchedNodeIds.contains(element.id);
          final bool isStartNode = element.kind == ElementKind.start;

          if (isNodeWatched || isStartNode) {
            // 自动关闭流程图界面
            if (_flowchartOverlay != null && _flowchartOverlay!.mounted && context.mounted) {
              _flowchartOverlay!.remove();
              _flowchartOverlay = null;
            }

            setState(() {
              currentIndex = elements.indexWhere((e) => e.id == element.id);
              isPlaying = true;
              playVideo();
            });
          } else {
            // 显示未解锁提示
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text(S.of(context).videoNodeLocked)),
            );
          }
        },
      ),
    );
  }

  Future<List<Element>> loadDashboard() async {
    // 根据项目类型选择正确的FLOWCHART.json文件路径
    final String jsonFilePath;
    final isWorkshopItem = widget.workshopItemId != '0';
    
    if (isWorkshopItem) {
      jsonFilePath = WorkshopPath.getFlowchartPath(widget.workshopItemId);
    } else {
      jsonFilePath = '$_projectPath/FLOWCHART.json';
    }
    
    print('加载FLOWCHART.json文件: $jsonFilePath');
    
    final File jsonFile = File(jsonFilePath);
    if (!await jsonFile.exists()) {
      print('FLOWCHART.json文件不存在: $jsonFilePath');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('${S.of(context).fileNotFound}: $jsonFilePath')),
      );
      return [];
    } 
    final String response = await jsonFile.readAsString();
    final data = await json.decode(response);
    data['projectPath'] = _projectPath;
    data['projectName'] = _projectName;
    dashboard.loadDashboardData(data);
    
    // 更新起始节点文本为当前语言的本地化文本
    _updateStartElementText();
  
    List<Element> elements = (data['elements'] as List)
        .map((element) => Element.fromJson(element))
        .toList();

    return elements;
  }

  // 更新起始节点文本为当前语言的本地化文本
  void _updateStartElementText() {
    // 检查是否已存在起始节点
    final existingStartElement = dashboard.elements.where((element) => element.kind == ElementKind.start).firstOrNull;
    
    if (existingStartElement != null) {
      // 如果存在起始节点，更新其文本为当前语言的本地化文本
      existingStartElement.text = S.of(context).startPoint;
    }
  }

  void checkForBranching(Duration position) {
    try {
      if (currentIndex < elements.length) {
        final currentElement = elements[currentIndex];

        // 解析结束时间，支持毫秒精度
        final endTimeParts = currentElement.endTime.split(':');
        int endHours = int.parse(endTimeParts[0]);
        int endMinutes = int.parse(endTimeParts[1]);
        int endSeconds = 0;
        int endMilliseconds = 0;
        
        String endSecondsPart = endTimeParts[2];
        if (endSecondsPart.contains('.')) {
          List<String> secondsAndMillis = endSecondsPart.split('.');
          endSeconds = int.parse(secondsAndMillis[0]);
          if (secondsAndMillis.length > 1) {
            endMilliseconds = int.parse(secondsAndMillis[1]);
          }
        } else {
          endSeconds = int.parse(endSecondsPart);
        }
        
        final endTime = Duration(
          hours: endHours,
          minutes: endMinutes,
          seconds: endSeconds,
          milliseconds: endMilliseconds,
        );
        
        // 检查是否是限时分支并获取显示时间
        int displayTime = 5; // 默认显示5秒
        bool isQteBranch = false;
        bool isTimedBranch = false;
        int qteDuration = 3; // 默认QTE持续3秒
        String? qteSuccessBranchId;
        String? qteFailBranchId;
        String? autoSelectBranchId;
        Offset qteButtonPosition = Offset.zero;
        
        // 解析分支设置数据
        if (currentElement.serializedData != null && currentElement.serializedData!.isNotEmpty) {
          try {
            final data = json.decode(currentElement.serializedData!) as Map<String, dynamic>;
            
            // 检查分支类型
            if (data.containsKey('branchType')) {
              int branchType = data['branchType'] as int;
              
              // 限时分支特殊处理
              if (branchType == 1) { // 1表示限时分支
                isTimedBranch = true;
                if (data.containsKey('autoSelectBranchId')) {
                  autoSelectBranchId = data['autoSelectBranchId'] as String;
                }
              }
              // QTE分支特殊处理
              else if (branchType == 2) { // 2表示QTE分支
                isQteBranch = true;
                
                // 读取QTE相关设置
                if (data.containsKey('qteDuration')) {
                  qteDuration = data['qteDuration'] as int;
                }
                if (data.containsKey('qteSuccessBranchId')) {
                  qteSuccessBranchId = data['qteSuccessBranchId'] as String;
                }
                if (data.containsKey('qteFailBranchId')) {
                  qteFailBranchId = data['qteFailBranchId'] as String;
                }
                if (data.containsKey('qteButtonPosition')) {
                  final posData = data['qteButtonPosition'] as Map<String, dynamic>;
                  qteButtonPosition = Offset(
                    posData['dx'] as double,
                    posData['dy'] as double
                  );
                }
              }
            }
            
            // 读取按钮显示时间，常规分支和限时分支都支持
            if (data.containsKey('timeLimit')) {
              displayTime = data['timeLimit'] as int;
            }
          } catch (e) {
            debugPrint('解析分支设置出错: $e');
          }
        }
        
        // 如果显示时间为0，则等到视频完全结束时再显示
        if (displayTime == 0) {
          return;
        }

        // 检查当前元素的next数组是否有多个分支
        if (currentElement.next.length > 1) {
          // 计算应该开始显示分支选项的时间点
          final showBranchTime = endTime - Duration(seconds: displayTime);
          
          // 如果当前位置已经到达或超过显示分支的时间点，且尚未显示分支选项
          if (position >= showBranchTime) {
            if (isQteBranch && !showQteButton) {
              // QTE分支：显示QTE按钮
              setState(() {
                showQteButton = true;
                
                // 设置QTE相关变量
                this.qteDuration = qteDuration;
                this.qteSuccessBranchId = qteSuccessBranchId;
                this.qteFailBranchId = qteFailBranchId;
                this.qteButtonPosition = qteButtonPosition;
                
                // 设置QTE定时器，如果用户没有点击，自动选择失败分支
                qteTimer?.cancel();
                qteTimer = Timer(Duration(seconds: qteDuration), () {
                  if (mounted && showQteButton) {
                    _handleQteFail(); // 时间到，自动走失败分支
                  }
                });
              });
              
              debugPrint('QTE按钮出现，持续时间: $qteDuration 秒，位置: (${qteButtonPosition.dx}, ${qteButtonPosition.dy})');
            }
            else if (!isQteBranch && !showBranchOptions) {
              // 常规分支和限时分支的处理
              // 安全地初始化烧绳线动画控制器，先清理旧的控制器
              if (_burnLineController != null) {
                _burnLineController!.dispose();
              }
              
              _burnLineController = AnimationController(
                duration: Duration(seconds: displayTime), // 使用配置的显示时间
                vsync: this,
              );
              
              _burnLineAnimation = Tween<double>(
                begin: 1.0, // 完整长度
                end: 0.0,   // 烧完后长度为0
              ).animate(CurvedAnimation(
                parent: _burnLineController!,
                curve: Curves.linear,
              ));
              
              // 启动动画
              _burnLineController!.forward();
              
              setState(() {
                showBranchOptions = true;
              });
              
              debugPrint('分支选项出现，烧绳线动画开始，显示时间: $displayTime 秒');
            }
          }
        }
      }
    } catch (e) {
      // 忽略错误，避免影响视频播放
      debugPrint('检查分支出错: $e');
    }
  }

  // 保存最近玩过的项目
  Future<void> _saveRecentProject() async {
    // 判断是本地项目还是创意工坊项目
    final isWorkshopItem = widget.workshopItemId != '0';
    
    // 创建 RecentProject 对象
    final recentProject = RecentProject(
      projectPath: _projectPath,
      projectName: _projectName,
      type: isWorkshopItem ? 'workshop' : 'local',
      lastPlayed: DateTime.now(),
    );
    
    // 获取项目封面图片
    if (isWorkshopItem) {
      // 工作坊项目：使用WorkshopPath获取第一张图片
      recentProject.imagePath = await WorkshopPath.getFirstImagePath(widget.workshopItemId);
    } else {
      // 本地项目：查找项目文件夹中的第一张图片
      try {
        final projectDir = Directory(_projectPath);
        if (await projectDir.exists()) {
          final imageFiles = await projectDir.list()
            .where((entity) => entity is File && 
                  (entity.path.toLowerCase().endsWith('.jpg') || 
                   entity.path.toLowerCase().endsWith('.jpeg') || 
                   entity.path.toLowerCase().endsWith('.png')))
            .toList();
          
          if (imageFiles.isNotEmpty) {
            recentProject.imagePath = (imageFiles.first as File).path;
          }
        }
      } catch (e) {
        print('获取本地项目封面图片时出错: $e');
      }
    }
    
    // 保存到 Data 中
    Data.saveRecentProject(recentProject);
  }

  void selectBranch(String nextId) {
    debugPrint('选择分支: $nextId, 隐藏分支选项');
    
    // 处理分支选择后的全局变量变化
    if (currentIndex >= 0 && currentIndex < elements.length) {
      final currentElement = elements[currentIndex];
      _processGlobalValueChanges(currentElement, nextId);
    }
    
    // 停止当前视频播放
    videoPlayerManager.player.stop();
    _positionSubscription?.cancel();
    
    setState(() {
      showBranchOptions = false;
      showQteButton = false;
      
      // 先标记当前节点为已观看（因为用户已经做出了选择）
      if (currentIndex >= 0 && currentIndex < elements.length) {
        final currentElement = elements[currentIndex];
        _markNodeAsWatched(currentElement.id);
      }
      
      // 然后再更新currentIndex到新选择的节点
      currentIndex = elements.indexWhere((element) => element.id == nextId);
      
      // 播放新的视频
      playVideo();
    });
  }

  // 处理分支选择后的全局变量变化
  void _processGlobalValueChanges(dynamic currentElement, String nextId) {
    if (currentElement.serializedData == null || currentElement.serializedData!.isEmpty) {
      return;
    }
    
    try {
      final data = json.decode(currentElement.serializedData!) as Map<String, dynamic>;
      
      // 检查是否有针对该分支的数值变化设置
      if (data.containsKey(nextId) && data[nextId].containsKey('valueChanges')) {
        Map<String, dynamic> valueChanges = data[nextId]['valueChanges'];
        
        // 检查当前节点是否已经在videoQueue中被标记为已完成
        bool isFirstTime = true;
        if (_projectArchive != null) {
          isFirstTime = !_projectArchive!.isNodeCompleted(currentElement.id);
        }
        
        // 处理每个全局变量的变化
        for (var varName in valueChanges.keys) {
          var change = valueChanges[varName];
          
          // 只处理启用的变化
          if (change['enabled'] == true) {
            // 获取当前全局变量的值
            final currentValue = _getGlobalVariableValue(varName);
            final changeValue = change['value'];
            dynamic newValue = currentValue;
            
            // 仅在首次观看时实际更新变量值
            if (isFirstTime) {
              // 根据操作符计算新值
              switch (change['operator']) {
                case '加上':
                  newValue = currentValue + changeValue;
                  break;
                case '减去':
                  newValue = currentValue - changeValue;
                  break;
                case '乘以':
                  newValue = currentValue * changeValue;
                  break;
                case '除以':
                  // 避免除以0
                  if (changeValue != 0) {
                    newValue = currentValue / changeValue;
                  } else {
                    newValue = currentValue;
                  }
                  break;
                case '设为':
                  newValue = changeValue;
                  break;
                default:
                  newValue = currentValue;
              }
              
              // 更新全局变量
              _updateGlobalVariable(varName, newValue);
              debugPrint('首次观看，更新全局变量 $varName: $currentValue -> $newValue');
            } else {
              // 非首次观看，不更新变量，仅记录日志
              debugPrint('非首次观看，不更新全局变量 $varName，保持值: $currentValue');
              
              // 对于非首次观看，计算"假"的新值用于显示动画，但不实际更新
              switch (change['operator']) {
                case '加上':
                  newValue = currentValue + changeValue;
                  break;
                case '减去':
                  newValue = currentValue - changeValue;
                  break;
                case '乘以':
                  newValue = currentValue * changeValue;
                  break;
                case '除以':
                  if (changeValue != 0) {
                    newValue = currentValue / changeValue;
                  }
                  break;
                case '设为':
                  newValue = changeValue;
                  break;
              }
            }
            
            // 无论是否首次观看，都显示数值变化动画
            if (mounted && context.mounted) {
              // 比较新旧值并显示相应的提示
              ValueChangeHelper.compareAndShow(
                context,
                oldValue: currentValue,
                newValue: newValue,
                valueName: varName, // 使用变量名称
                position: Alignment.topRight, // 右上角显示数值变化
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('处理全局变量变化出错: $e');
    }
  }

  String getElementTextById(String id) {
    final element = elements.firstWhere((element) => element.id == id);
    return element.text;
  }

  void playVideo() {
    _positionSubscription?.cancel();
  
    if (currentIndex < elements.length) {
      final element = elements[currentIndex];
      String videoPath = element.path;
      
      // 更新当前节点存档
      _updateCurrentNodeArchive(element.id);

      if (videoPath.isEmpty) {
        if (element.next.isEmpty) {
          if (mounted) {
            setState(() {
              isPlaying = false; // 所有视频播放完成，显示Dashboard
            });
          }
          return;
        }
        currentIndex = elements.indexWhere((e) => e.id == element.next[0]);
        playVideo();
        return;
      }

      // 如果是相对路径，添加项目路径前缀
      if (!videoPath.contains('/') && !videoPath.contains('\\')) {
        videoPath = '$_projectPath/$videoPath';
        print(S.current.fullVideoPath + videoPath);
      }
      
      // 检查视频文件是否存在
      final videoFile = File(videoPath);
      if (!videoFile.existsSync()) {
        print(S.current.videoFileNotExist + videoPath);
        // 显示错误提示
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${S.of(context).fileNotFound}: $videoPath')),
          );
        }
        // 如果有下一个元素，尝试播放下一个
        if (element.next.isNotEmpty) {
          currentIndex = elements.indexWhere((e) => e.id == element.next[0]);
          playVideo();
        } else {
          if (mounted) {
            setState(() {
              isPlaying = false; // 显示Dashboard
            });
          }
        }
        return;
      }

      // 解析起始时间和结束时间，支持毫秒精度
      // 起始时间解析
      final startTimeParts = element.startTime.split(':');
      int startHours = int.parse(startTimeParts[0]);
      int startMinutes = int.parse(startTimeParts[1]);
      int startSeconds = 0;
      int startMilliseconds = 0;
      
      String startSecondsPart = startTimeParts[2];
      if (startSecondsPart.contains('.')) {
        List<String> secondsAndMillis = startSecondsPart.split('.');
        startSeconds = int.parse(secondsAndMillis[0]);
        if (secondsAndMillis.length > 1) {
          startMilliseconds = int.parse(secondsAndMillis[1]);
        }
      } else {
        startSeconds = int.parse(startSecondsPart);
      }
      
      final startTimeDuration = Duration(
        hours: startHours,
        minutes: startMinutes,
        seconds: startSeconds,
        milliseconds: startMilliseconds,
      );

      // 结束时间解析
      final endTimeParts = element.endTime.split(':');
      int endHours = int.parse(endTimeParts[0]);
      int endMinutes = int.parse(endTimeParts[1]);
      int endSeconds = 0;
      int endMilliseconds = 0;
      
      String endSecondsPart = endTimeParts[2];
      if (endSecondsPart.contains('.')) {
        List<String> secondsAndMillis = endSecondsPart.split('.');
        endSeconds = int.parse(secondsAndMillis[0]);
        if (secondsAndMillis.length > 1) {
          endMilliseconds = int.parse(secondsAndMillis[1]);
        }
      } else {
        endSeconds = int.parse(endSecondsPart);
      }
      
      final endTimeDuration = Duration(
        hours: endHours,
        minutes: endMinutes,
        seconds: endSeconds,
        milliseconds: endMilliseconds,
      );

      // 视频完整时长
      final videoDuration = endTimeDuration - startTimeDuration;

      try {
        // 重置状态
        setState(() {
          showBranchOptions = false;
          showQteButton = false;
        });
        
        // 取消已有的定时器
        qteTimer?.cancel();
        
        // 注意：这里不再释放_burnLineController，而是设置为null
        // 避免重复调用dispose
        _burnLineController = null;
        _burnLineAnimation = null;
        
        // 直接打开完整视频
        videoPlayerManager.player.open(Media(videoPath));
        
        // 立即开始播放，以触发playing事件
        videoPlayerManager.player.play();
        
        // 等待视频加载后再跳转到起始位置
        var initialSeekDone = false;
        
        // 监听视频状态，用playing作为视频已加载的指标
        final readySubscription = videoPlayerManager.player.stream.playing.listen((isPlaying) {
          if (isPlaying && !initialSeekDone) {
            // 确保视频已经开始播放后，立即跳转到起始位置
            videoPlayerManager.player.seek(startTimeDuration);
            initialSeekDone = true;
          }
        });
        
        // 监听位置变化
        _positionSubscription = videoPlayerManager.player.stream.position.listen((position) {
          // 如果还未到达起始位置或位置异常，重新定位
          if (position < startTimeDuration - Duration(milliseconds: 100)) {
            videoPlayerManager.player.seek(startTimeDuration);
            return;
          }
          
          // 计算结束判断位置（提前一点点结束，避免卡在最后一帧）
          final effectiveEndTime = endTimeDuration.inMilliseconds > 1000 
              ? endTimeDuration - Duration(milliseconds: 200) 
              : endTimeDuration;
                
          // 检查是否到达结束位置
          if (position >= effectiveEndTime) {
            // 标记当前节点为已观看
            _markNodeAsWatched(element.id);
            
            // 避免重复处理
            _positionSubscription?.cancel();
            readySubscription.cancel();
            
            if (showBranchOptions) {
              // 检查是否是限时分支
              bool isTimedBranch = false;
              String? autoSelectBranchId;
              
              if (element.serializedData != null && element.serializedData!.isNotEmpty) {
                try {
                  final data = json.decode(element.serializedData!) as Map<String, dynamic>;
                  if (data.containsKey('branchType') && data['branchType'] == 1) {
                    isTimedBranch = true;
                    
                    // 读取自动选择分支ID
                    if (data.containsKey('autoSelectBranchId')) {
                      autoSelectBranchId = data['autoSelectBranchId'] as String;
                    }
                  }
                } catch (e) {
                  debugPrint('解析分支类型数据出错: $e');
                }
              }
              
              // 限时分支自动选择指定分支
              if (isTimedBranch && element.next.isNotEmpty) {
                // 默认选择第一个满足条件的分支ID
                String? nextId;
                List<String> validBranches = [];
                
                // 筛选满足条件的分支
                for (var branchId in element.next) {
                  bool shouldShowBranch = true;
                  
                  // 检查分支是否满足全局变量条件
                  if (element.serializedData != null && element.serializedData!.isNotEmpty) {
                    try {
                      final data = json.decode(element.serializedData!) as Map<String, dynamic>;
                      // 检查是否有针对该分支的条件设置
                      if (data.containsKey(branchId) && 
                          data[branchId].containsKey('valueConditions')) {
                        
                        Map<String, dynamic> valueConditions = data[branchId]['valueConditions'];
                        
                        // 检查条件是否满足
                        for (var varName in valueConditions.keys) {
                          var condition = valueConditions[varName];
                          // 只检查启用的条件
                          if (condition['enabled'] == true) {
                            // 获取当前全局变量的值
                            final varValue = _getGlobalVariableValue(varName);
                            
                            // 根据操作符检查条件
                            String operator = condition['operator'];
                            bool conditionResult = false;

                            if (operator == '范围') {
                              // 范围检查
                              final rangeStart = condition['rangeStart'];
                              final rangeEnd = condition['rangeEnd'];
                              conditionResult = (varValue >= rangeStart && varValue <= rangeEnd);
                            } else {
                              // 其他操作符检查
                              final compareValue = condition['value'];
                              
                              switch (operator) {
                                case '大于':
                                  conditionResult = varValue > compareValue;
                                  break;
                                case '小于':
                                  conditionResult = varValue < compareValue;
                                  break;
                                case '等于':
                                  conditionResult = varValue == compareValue;
                                  break;
                                case '大于等于':
                                  conditionResult = varValue >= compareValue;
                                  break;
                                case '小于等于':
                                  conditionResult = varValue <= compareValue;
                                  break;
                                case '不等于':
                                  conditionResult = varValue != compareValue;
                                  break;
                                default:
                                  conditionResult = true; // 未知操作符默认为true
                                  break;
                              }
                            }
                            
                            // 如果条件不满足，标记不显示该分支并退出循环
                            if (!conditionResult) {
                              shouldShowBranch = false;
                              break;
                            }
                          }
                        }
                      }
                    } catch (e) {
                      debugPrint('检查分支条件出错: $e');
                    }
                  }
                  
                  // 如果分支满足条件，添加到有效分支列表
                  if (shouldShowBranch) {
                    validBranches.add(branchId);
                  }
                }
                
                // 如果有自动选择分支ID，且该分支有效，则优先选择
                if (autoSelectBranchId != null && validBranches.contains(autoSelectBranchId)) {
                  nextId = autoSelectBranchId;
                } 
                // 否则选择第一个有效分支
                else if (validBranches.isNotEmpty) {
                  nextId = validBranches.first;
                }
                // 没有有效分支时，选择第一个分支
                else if (element.next.isNotEmpty) {
                  nextId = element.next.first;
                }
                
                if (nextId != null) {
                  // 处理分支选择后的全局变量变化
                  _processGlobalValueChanges(element, nextId);
                  
                  setState(() {
                    showBranchOptions = false;
                    
                    // 先标记当前节点为已观看（因为用户已经看到了分支选项）
                    _markNodeAsWatched(element.id);
                    
                    // 然后再更新currentIndex到新选择的节点
                    currentIndex = elements.indexWhere((element) => element.id == nextId);
                    
                    // 停止当前视频
                    videoPlayerManager.player.stop();
                    readySubscription.cancel();
                    _positionSubscription?.cancel();
                    
                    // 播放下一个视频
                    playVideo();
                  });
                }
              }
            } else if (showQteButton) {
              // QTE分支：如果显示QTE按钮但视频已经结束，自动选择失败分支
              _handleQteFail();
            } else if (element.next.isEmpty) {
              // 没有分支选项且视频播放完毕：返回主界面
              if (mounted) {
                setState(() {
                  isPlaying = false; // 视频播放完成，显示Dashboard
                });
              }
              // 停止当前视频
              videoPlayerManager.player.stop();
              readySubscription.cancel();
              _positionSubscription?.cancel();
            } else if (element.next.length == 1) {
              // 只有一个分支选项：自动选择该分支
              
              // 处理分支选择后的全局变量变化
              _processGlobalValueChanges(element, element.next[0]);
              
              setState(() {
                // 更新currentIndex到下一个节点
                currentIndex = elements.indexWhere((e) => e.id == element.next[0]);
                
                // 停止当前视频
                videoPlayerManager.player.stop();
                readySubscription.cancel();
                _positionSubscription?.cancel();
                
                // 播放下一个视频
                playVideo();
              });
            }
          } else {
            // 检查是否需要显示分支选项
            checkForBranching(position);
          }
        });
      } catch (e) {
        print(S.current.videoPlaybackError + '$e');
        if (mounted && context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(S.of(context).videoPlaybackError + '$e: $videoPath')),
          );
        }
      }
    }
  }
  /// 更新当前节点存档
  Future<void> _updateCurrentNodeArchive(String nodeId) async {
    if (_projectArchive != null && _startElementId != null) {
      // 更新当前节点，将视频添加到队列
      _projectArchive = _projectArchive!.addVideoToQueue(nodeId);
      
      // 保存存档
      await SaveArchive.saveArchive(_projectArchive!);
      print(S.current.archiveUpdatedForNode + nodeId);
    }
  }
  
  /// 标记节点为已观看
  Future<void> _markNodeAsWatched(String nodeId) async {
    if (_projectArchive != null && _startElementId != null) {
      // 检查节点是否已经标记为观看过
      if (!_watchedNodeIds.contains(nodeId)) {
        // 更新已观看节点集合
        _watchedNodeIds.add(nodeId);
        
        // 更新存档，标记节点为已完成
        _projectArchive = _projectArchive!.markNodeAsCompleted(nodeId);
        
        // 保存存档
        await SaveArchive.saveArchive(_projectArchive!);
        print(S.current.nodeMarkedAsWatched + nodeId);
      }
    }
  }
  
  // 移除了视频播放完成后的金币奖励逻辑，改为在上传创意工坊后奖励金币
  
  @override
  void dispose() {
    _positionSubscription?.cancel();
    
    // 释放烧绳线动画控制器 - 仅当控制器存在时才释放
    if (_burnLineController != null) {
      _burnLineController!.dispose();
      _burnLineController = null;
    }
    
    // 取消QTE计时器
    qteTimer?.cancel();
    qteProgressDecayTimer?.cancel();
    
    // 检查流程图覆盖层是否已创建并尚未移除
    if (isPlaying == false && _flowchartOverlay != null) {
      try {
        if (_flowchartOverlay!.mounted && context.mounted) {
          _flowchartOverlay!.remove();
          _flowchartOverlay = null;
        }
      } catch (e) {
        print('移除流程图覆盖层时出错: $e');
      }
    }
    
    videoPlayerManager.dispose();
    super.dispose();
  }

  // 添加Overlay相关变量
  OverlayEntry? _flowchartOverlay;

  void _showFlowchartOverlay() {
    // 如果已有覆盖层，先安全移除
    if (_flowchartOverlay != null) {
      try {
        if (_flowchartOverlay!.mounted && context.mounted) {
          _flowchartOverlay!.remove();
        }
      } catch (e) {
        print('移除流程图覆盖层时出错: $e');
      }
      _flowchartOverlay = null;
    }
    
    // 创建新的覆盖层
    _flowchartOverlay = OverlayEntry(
      builder: (context) => Positioned.fill(
        child: Container(
          color: Colors.black87,
          child: Column(
            children: [
              AppBar(
                title: Text(S.of(context).flowchart),
                actions: [
                  IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () {
                      try {
                        if (_flowchartOverlay != null && _flowchartOverlay!.mounted && context.mounted) {
                          _flowchartOverlay!.remove();
                          _flowchartOverlay = null;
                        }
                      } catch (e) {
                        print('关闭流程图覆盖层时出错: $e');
                      }
                      
                      setState(() {
                        isPlaying = true;
                      });
                      videoPlayerManager.player.play();
                    },
                  ),
                ],
              ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: buildDashboard(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
    
    // 安全插入覆盖层
    if (mounted && context.mounted) {
      try {
        Overlay.of(context).insert(_flowchartOverlay!);
      } catch (e) {
        print('插入流程图覆盖层时出错: $e');
        _flowchartOverlay = null;
      }
    }
  }

  // 鼠标悬停状态变量
  bool _isHovering = false;
  
  // 构建带有悬停控制按钮的视频播放器
  Widget _buildVideoPlayerWithControls() {
    // 从全局设置中获取是否启用视频点击暂停播放
    final bool enableVideoClickPause = _prefs?.getBool('enableVideoClickPause') ?? false;
    
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovering = false), // 初始进入时不显示控制按钮
      onExit: (_) => setState(() => _isHovering = false),
      onHover: (PointerHoverEvent event) {
        // 获取视频播放器的渲染框
        final RenderBox? box = context.findRenderObject() as RenderBox?;
        if (box != null) {
          // 计算视频高度的1/4位置
          final Size size = box.size;
          final double topQuarterHeight = size.height / 4;
          
          // 检查鼠标是否在上方1/4区域内
          final bool isInTopQuarter = event.localPosition.dy <= topQuarterHeight;
          
          // 只有当鼠标在上方1/4区域时才显示控制按钮
          setState(() => _isHovering = isInTopQuarter);
        }
      },
      child: Stack(
        children: [
          // 视频播放器
          videoPlayerManager.buildVideoWidget(
            context, 
            showControls: showController,
            enableClickPause: enableVideoClickPause,
          ),
          
          // 左上角返回按钮，仅在悬停时显示
          if (_isHovering)
            Positioned(
              left: 16,
              top: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                  tooltip: S.of(context).back,
                  iconSize: 20,
                  padding: EdgeInsets.all(4),
                  constraints: BoxConstraints(),
                ),
              ),
            ),
          
          // 右上角显示控制器按钮，仅在悬停时显示
          if (_isHovering)
            Positioned(
              right: 136, // 向左移动，为全屏按钮腾出空间
              top: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(
                    showController ? Icons.visibility_off : Icons.visibility,
                    color: Colors.white
                  ),
                  onPressed: () {
                    setState(() {
                      showController = !showController;
                      
                      // 保存设置到项目特定的键名
                      if (_prefs != null) {
                        final String projectId = _projectPath;
                        final String showVideoControlsKey = 'showVideoControls_$projectId';
                        _prefs!.setBool(showVideoControlsKey, showController);
                      }
                    });
                  },
                  tooltip: showController ? S.of(context).hideController : S.of(context).showController,
                  iconSize: 20,
                  padding: EdgeInsets.all(4),
                  constraints: BoxConstraints(),
                ),
              ),
            ),
            
          // 右上角全屏切换按钮，仅在悬停时显示
          if (_isHovering)
            Positioned(
              right: 96, // 在显示控制器按钮和流程图按钮之间
              top: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(
                    _fullscreenManager.isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
                    color: Colors.white
                  ),
                  onPressed: () {
                    _fullscreenManager.toggleFullscreen();
                    // 移除这行，避免重复设置全屏状态
                    // FullScreenWindow.setFullScreen(_fullscreenManager.isFullscreen);
                  },
                  tooltip: _fullscreenManager.isFullscreen ? S.of(context).exitFullscreen : S.of(context).fullscreen,
                  iconSize: 20,
                  padding: EdgeInsets.all(4),
                  constraints: BoxConstraints(),
                ),
              ),
            ),
            
          // 右上角显示流程图按钮，仅在悬停时显示
          if (_isHovering)
            Positioned(
              right: 56, // 在显示控制器按钮和设置按钮之间
              top: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(Icons.account_tree, color: Colors.white),
                  onPressed: () {
                    // 暂停视频
                    videoPlayerManager.player.pause();
                    
                    // 显示流程图悬浮层
                    _showFlowchartOverlay();
                  },
                  tooltip: S.of(context).flowchart,
                  iconSize: 20,
                  padding: EdgeInsets.all(4),
                  constraints: BoxConstraints(),
                ),
              ),
            ),
            
          // 右上角设置按钮，仅在悬停时显示
          if (_isHovering)
            Positioned(
              right: 16,
              top: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: const Color.fromRGBO(0, 0, 0, 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: Icon(Icons.settings, color: Colors.white),
                  onPressed: () {
                    // 暂停视频
                    videoPlayerManager.player.pause();
                    
                    // 导航到设置界面而不是显示悬浮窗口
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => SettingsScreen(
                          projectId: _projectPath,
                        ),
                      ),
                    ).then((_) {
                      // 当设置界面关闭后，重新加载设置并恢复播放
                      _loadSettings();
                      videoPlayerManager.player.play();
                    });
                  },
                  tooltip: S.of(context).settings,
                  iconSize: 20,
                  padding: EdgeInsets.all(4),
                  constraints: BoxConstraints(),
                ),
              ),
            ),
        ],
      ),
    );
  }
  
  // 加载设置
  Future<void> _loadSettings() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      
      if (_prefs != null) {
        // 使用项目路径作为项目ID，确保每个项目有独立的设置
        final String projectId = _projectPath;
        final String showVideoControlsKey = 'showVideoControls_$projectId';
        final String volumeKey = 'volume_$projectId';
        
        setState(() {
          // 加载项目特定的设置
          showController = _prefs!.getBool(showVideoControlsKey) ?? false;
          
          // 加载并设置音量
          final double volume = _prefs!.getDouble(volumeKey) ?? 0.8; // 默认音量80%
          videoPlayerManager.player.setVolume(volume * 100); // MediaKit音量范围是0-100
          
          // 加载全局播放速度设置
          videoPlayerManager.setPlaybackRate(_prefs!.getDouble('playbackRate') ?? 1.0);
        });
      }
    } catch (e) {
      debugPrint('加载设置出错: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: isPlaying ? null : AppBar(
        title: Text(S.of(context).openGameTitle),
        actions: [
          IconButton(
            icon: Icon(showController ? Icons.visibility_off : Icons.visibility),
            onPressed: () {
              setState(() {
                showController = !showController;
              });
            },
          ),
        ],
      ),
      body: Stack(
        children: [
          SafeArea(
            child: isPlaying
                ? _buildVideoPlayerWithControls()
                : buildDashboard(),
          ),
          
          // 金币获取动画
          if (_showingCoinAnimation)
            CoinAnimation(
              coinAmount: 10,
              onAnimationComplete: () {
                if (mounted) {
                  setState(() {
                    _showingCoinAnimation = false;
                  });
                }
              },
            ),
          if (showBranchOptions)
            _buildSimpleBranchOptions(),
          if (showQteButton)
            _buildQteButton(),
        ],
      ),
    );
  }

  Widget _buildSimpleBranchOptions() {
    final currentElement = elements[currentIndex];
    
    // 检查是否设置了仅显示文字选项
    bool buttonTextOnly = false;
    if (currentElement.serializedData != null && currentElement.serializedData!.isNotEmpty) {
      try {
        final data = json.decode(currentElement.serializedData!) as Map<String, dynamic>;
        if (data.containsKey('buttonTextOnly')) {
          buttonTextOnly = data['buttonTextOnly'] as bool;
        }
      } catch (e) {
        debugPrint('解析按钮仅显示文字选项出错: $e');
      }
    }
    
    // 创建分支按钮列表
    return Positioned.fill(
      child: Stack(
        children: [
          // 分支标题
          if (currentElement.branchTitle != null && currentElement.branchTitle!.isNotEmpty)
            Positioned(
              top: 80,
              left: 0,
              right: 0,
              child: Center(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(0, 0, 0, 0.7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    currentElement.branchTitle!,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            
          // 分支按钮 - 简化为中间布局
          ...currentElement.next.asMap().entries.map((entry) {
            final int index = entry.key;
            final String nextId = entry.value;
            
            // 获取分支的全局值条件，并检查是否满足条件
            bool shouldShowBranch = true; // 默认显示该分支
            
            // 从serializedData中读取分支特定的设置
            if (currentElement.serializedData != null && currentElement.serializedData!.isNotEmpty) {
              try {
                final data = json.decode(currentElement.serializedData!) as Map<String, dynamic>;
                // 检查是否有针对该分支的条件设置
                if (data.containsKey(nextId) && 
                    data[nextId].containsKey('valueConditions')) {
                  
                  Map<String, dynamic> valueConditions = data[nextId]['valueConditions'];
                  
                  // 检查条件是否满足
                  for (var varName in valueConditions.keys) {
                    var condition = valueConditions[varName];
                    // 只检查启用的条件
                    if (condition['enabled'] == true) {
                      // 获取当前全局变量的值
                      final varValue = _getGlobalVariableValue(varName);
                      
                      // 根据操作符检查条件
                      String operator = condition['operator'];
                      bool conditionResult = false;

                      if (operator == '范围') {
                        // 范围检查
                        final rangeStart = condition['rangeStart'];
                        final rangeEnd = condition['rangeEnd'];
                        conditionResult = (varValue >= rangeStart && varValue <= rangeEnd);
                      } else {
                        // 其他操作符检查
                        final compareValue = condition['value'];
                        
                        switch (operator) {
                          case '大于':
                            conditionResult = varValue > compareValue;
                            break;
                          case '小于':
                            conditionResult = varValue < compareValue;
                            break;
                          case '等于':
                            conditionResult = varValue == compareValue;
                            break;
                          case '大于等于':
                            conditionResult = varValue >= compareValue;
                            break;
                          case '小于等于':
                            conditionResult = varValue <= compareValue;
                            break;
                          case '不等于':
                            conditionResult = varValue != compareValue;
                            break;
                          default:
                            conditionResult = true; // 未知操作符默认为true
                            break;
                        }
                      }
                      
                      // 如果条件不满足，标记不显示该分支并退出循环
                      if (!conditionResult) {
                        shouldShowBranch = false;
                        break;
                      }
                    }
                  }
                }
              } catch (e) {
                debugPrint('检查分支条件出错: $e');
              }
            }
            
            // 如果条件不满足，跳过这个分支
            if (!shouldShowBranch) {
              return const SizedBox.shrink(); // 返回空组件，不显示这个分支
            }
            
            // 计算简单布局位置
            final int count = currentElement.next.length;
            double step = 1.0 / (count + 1);
            final double xPos = (index + 1) * step;
            
            // 获取按钮文字 - 修改为使用分支按钮文字而不是节点标题
            String buttonText = '';
            // 首先尝试获取分支按钮设置的文字
            if (currentElement.branchButtonTexts != null && 
                currentElement.branchButtonTexts!.containsKey(nextId) &&
                currentElement.branchButtonTexts![nextId]!.isNotEmpty) {
              buttonText = currentElement.branchButtonTexts![nextId]!;
            } else {
              // 如果没有设置按钮文字，才使用节点标题作为后备
              buttonText = getElementTextById(nextId);
            }
            
            // 判断分支类型并决定是否显示烧绳线
            bool isTimedBranch = false;
            String? autoSelectBranchId;
            
            // 尝试从serializedData读取分支类型
            if (currentElement.serializedData != null && currentElement.serializedData!.isNotEmpty) {
              try {
                final data = json.decode(currentElement.serializedData!) as Map<String, dynamic>;
                if (data.containsKey('branchType') && data['branchType'] == 1) {
                  isTimedBranch = true; // 限时分支
                  
                  // 读取自动选择分支ID
                  if (data.containsKey('autoSelectBranchId')) {
                    autoSelectBranchId = data['autoSelectBranchId'] as String;
                  }
                }
              } catch (e) {
                debugPrint('解析分支类型数据出错: $e');
              }
            }
            
            // 判断当前按钮是否是默认选择的分支
            bool isDefaultBranch = false;
            if (isTimedBranch) {
              if (autoSelectBranchId != null && nextId == autoSelectBranchId) {
                isDefaultBranch = true; // 如果配置了自动选择分支，检查当前分支是否匹配
              } else if (autoSelectBranchId == null && index == 0) {
                isDefaultBranch = true; // 如果没有配置自动选择分支，默认第一个为默认分支
              }
            }
            
            // 检查是否有自定义位置设置
            if (currentElement.branchButtonPositions != null && 
                currentElement.branchButtonPositions!.containsKey(nextId)) {
              // 使用自定义位置
              Offset customPosition = currentElement.branchButtonPositions![nextId]!;
              return Positioned(
                left: customPosition.dx * MediaQuery.of(context).size.width - 75,
                top: customPosition.dy * MediaQuery.of(context).size.height - 25,
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    // 只有限时分支的默认按钮才显示烧绳线动画
                    if (isTimedBranch && isDefaultBranch)
                      AnimatedBuilder(
                        animation: _burnLineAnimation ?? const AlwaysStoppedAnimation(1.0),
                        builder: (context, child) {
                          return BurnLineWidget(
                            progress: _burnLineAnimation?.value ?? 1.0,
                            lineColor: Colors.red,
                            fireColor: Colors.orange,
                            lineWidth: 3.0,
                            width: 158.0,
                            height: 58.0,
                            borderRadius: BorderRadius.circular(8),
                          );
                        },
                      ),
                    
                    // 按钮 - 根据buttonTextOnly决定是使用ElevatedButton还是TextButton
                    buttonTextOnly || (currentElement.branchButtonOpacity != null && currentElement.branchButtonOpacity! <= 0.0)
                    ? TextButton(
                        onPressed: () => selectBranch(nextId),
                        child: Text(
                          buttonText,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.white,
                            // 添加文字阴影以增强可见性
                            shadows: [
                              Shadow(
                                blurRadius: 3.0,
                                color: Colors.black87,
                                offset: Offset(1.0, 1.0),
                              ),
                              Shadow(
                                blurRadius: 3.0,
                                color: Colors.black54,
                                offset: Offset(-1.0, -1.0),
                              ),
                            ],
                          ),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : ElevatedButton(
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white.withOpacity(currentElement.branchButtonOpacity ?? 0.9),
                          foregroundColor: Colors.black,
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                          minimumSize: const Size(150, 50),
                          fixedSize: const Size(150, 50),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        onPressed: () => selectBranch(nextId),
                        child: Text(
                          buttonText,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ],
                ),
              );
            }
            
            // 构建默认按钮位置和样式
            final double bottomPosition = isDefaultBranch ? 80 : 100;
            final double leftOffset = isDefaultBranch ? 90 : 75;
            
            return Positioned(
              left: xPos * MediaQuery.of(context).size.width - leftOffset,
              bottom: bottomPosition,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 只有限时分支的默认按钮才显示烧绳线动画
                  if (isTimedBranch && isDefaultBranch)
                    AnimatedBuilder(
                      animation: _burnLineAnimation ?? const AlwaysStoppedAnimation(1.0),
                      builder: (context, child) {
                        return BurnLineWidget(
                          progress: _burnLineAnimation?.value ?? 1.0,
                          lineColor: Colors.red,
                          fireColor: Colors.orange,
                          lineWidth: 3.0,
                          width: 158.0,
                          height: 58.0,
                          borderRadius: BorderRadius.circular(8),
                        );
                      },
                    ),
                  
                  // 按钮 - 根据buttonTextOnly决定是使用ElevatedButton还是TextButton
                  buttonTextOnly || (currentElement.branchButtonOpacity != null && currentElement.branchButtonOpacity! <= 0.0)
                  ? TextButton(
                      onPressed: () => selectBranch(nextId),
                      child: Text(
                        buttonText,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                          // 添加文字阴影以增强可见性
                          shadows: [
                            Shadow(
                              blurRadius: 3.0,
                              color: Colors.black87,
                              offset: Offset(1.0, 1.0),
                            ),
                            Shadow(
                              blurRadius: 3.0,
                              color: Colors.black54,
                              offset: Offset(-1.0, -1.0),
                            ),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.white.withOpacity(currentElement.branchButtonOpacity ?? 0.9),
                        foregroundColor: Colors.black,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        minimumSize: const Size(150, 50),
                        fixedSize: const Size(150, 50),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      onPressed: () => selectBranch(nextId),
                      child: Text(
                        buttonText,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  // 处理QTE成功
  void _handleQteSuccess() {
    debugPrint('QTE成功，选择成功分支: $qteSuccessBranchId');
    
    // 取消QTE计时器
    qteTimer?.cancel();
    qteTimer = null;
    
    // 取消进度衰减计时器
    qteProgressDecayTimer?.cancel();
    qteProgressDecayTimer = null;
    
    // 停止当前视频
    videoPlayerManager.player.stop();
    _positionSubscription?.cancel();
    
    setState(() {
      showQteButton = false;
      qteCurrentClicks = 0;
      qteProgress = 0.0;
      
      // 先标记当前节点为已观看
      if (currentIndex >= 0 && currentIndex < elements.length) {
        final currentElement = elements[currentIndex];
        _markNodeAsWatched(currentElement.id);
      }
      
      // 选择成功分支
      if (qteSuccessBranchId != null) {
        currentIndex = elements.indexWhere((element) => element.id == qteSuccessBranchId);
        playVideo();
      }
    });
  }
  
  // 处理QTE失败
  void _handleQteFail() {
    debugPrint('QTE失败，选择失败分支: $qteFailBranchId');
    
    // 取消QTE计时器
    qteTimer?.cancel();
    qteTimer = null;
    
    // 取消进度衰减计时器
    qteProgressDecayTimer?.cancel();
    qteProgressDecayTimer = null;
    
    // 停止当前视频
    videoPlayerManager.player.stop();
    _positionSubscription?.cancel();
    
    setState(() {
      showQteButton = false;
      qteCurrentClicks = 0;
      qteProgress = 0.0;
      
      // 先标记当前节点为已观看
      if (currentIndex >= 0 && currentIndex < elements.length) {
        final currentElement = elements[currentIndex];
        _markNodeAsWatched(currentElement.id);
      }
      
      // 选择失败分支
      if (qteFailBranchId != null) {
        currentIndex = elements.indexWhere((element) => element.id == qteFailBranchId);
        playVideo();
      }
    });
  }

  // 处理QTE点击
  void _handleQteClick() {
    // 增加点击计数
    qteCurrentClicks++;
    
    // 增加进度条进度
    setState(() {
      // 每次点击增加进度，从1.0/qteClicksRequired改为更大的值，确保3次点击能完成
      qteProgress += 0.35; // 每次点击增加35%进度，3次就能超过100%
      
      // 限制最大值为1.0
      if (qteProgress > 1.0) qteProgress = 1.0;
      
      // 如果达到要求点击次数，触发成功
      if (qteProgress >= 1.0) {
        _handleQteSuccess();
        return;
      }
    });
    
    // 启动或重置进度衰减计时器
    qteProgressDecayTimer?.cancel();
    qteProgressDecayTimer = Timer.periodic(Duration(milliseconds: 100), (timer) {
      setState(() {
        // 缓慢衰减进度，从0.05改为更小的值，使进度条不那么快衰减
        qteProgress -= 0.03;
        
        // 如果进度归零，停止衰减
        if (qteProgress <= 0.0) {
          qteProgress = 0.0;
          timer.cancel();
        }
      });
    });
  }

  // 构建QTE按钮
  Widget _buildQteButton() {
    final size = MediaQuery.of(context).size;
    
    return Positioned.fill(
      child: Stack(
        children: [
          // QTE按钮
          Positioned(
            left: qteButtonPosition.dx * size.width - 50, // 按钮中心位置
            top: qteButtonPosition.dy * size.height - 50, // 按钮中心位置
            child: GestureDetector(
              onTap: _handleQteClick, // 点击处理
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 外圈倒计时环
                  SizedBox(
                    width: 100,
                    height: 100,
                    child: TweenAnimationBuilder<double>(
                      tween: Tween<double>(begin: 1.0, end: 0.0),
                      duration: Duration(seconds: qteDuration),
                      onEnd: () {
                        // 倒计时结束，如果还没到达成功状态，则失败
                        if (showQteButton && qteProgress < 1.0) {
                          _handleQteFail();
                        }
                      },
                      builder: (context, timeValue, child) {
                        return CircularProgressIndicator(
                          value: timeValue,
                          strokeWidth: 4.0,
                          color: Colors.red,
                          backgroundColor: Colors.red.withOpacity(0.3),
                        );
                      },
                    ),
                  ),
                  
                  // 内圈进度指示器
                  SizedBox(
                    width: 80,
                    height: 80,
                    child: CircularProgressIndicator(
                      value: qteProgress,
                      strokeWidth: 8.0,
                      color: Colors.green,
                      backgroundColor: Colors.grey.withOpacity(0.3),
                    ),
                  ),
                  
                  // 动态缩放的中心按钮
                  TweenAnimationBuilder<double>(
                    tween: Tween<double>(begin: 0.9, end: 1.1),
                    duration: Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                    builder: (context, pulseValue, child) {
                      return Transform.scale(
                        scale: pulseValue,
                        child: Container(
                          width: 60,
                          height: 60,
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.8),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.red.withOpacity(0.5),
                                blurRadius: 10,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Stack(
                            alignment: Alignment.center,
                            children: [
                              // 点击图标
                              Icon(
                                Icons.touch_app,
                                color: Colors.white,
                                size: 30,
                              ),
                              
                              // 快速点击文字提示
                              Positioned(
                                bottom: 10,
                                child: Text(
                                  "快速点击",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// 烧绳线Widget
class BurnLineWidget extends StatelessWidget {
  final double progress; // 0.0-1.0，表示剩余未烧完的绳子比例
  final Color lineColor;
  final Color fireColor;
  final double lineWidth;
  final double width;
  final double height;
  final BorderRadius borderRadius;
  
  const BurnLineWidget({
    Key? key,
    required this.progress,
    this.lineColor = Colors.red,
    this.fireColor = Colors.orange,
    this.lineWidth = 3.0,
    this.width = 158.0,
    this.height = 58.0,
    this.borderRadius = const BorderRadius.all(Radius.circular(8.0)),
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width + lineWidth * 2,
      height: height + lineWidth * 2,
      child: CustomPaint(
        painter: BurnLinePainter(
          progress: progress,
          lineColor: lineColor,
          fireColor: fireColor,
          lineWidth: lineWidth,
          borderRadius: borderRadius,
        ),
      ),
    );
  }
}

// 烧绳线绘制器
class BurnLinePainter extends CustomPainter {
  final double progress;
  final Color lineColor;
  final Color fireColor;
  final double lineWidth;
  final BorderRadius borderRadius;
  
  BurnLinePainter({
    required this.progress,
    required this.lineColor,
    required this.fireColor,
    required this.lineWidth,
    required this.borderRadius,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    // 计算内部矩形，考虑线宽
    final rect = Rect.fromLTWH(
      lineWidth / 2, 
      lineWidth / 2, 
      size.width - lineWidth, 
      size.height - lineWidth
    );
    
    // 计算矩形周长
    final perimeter = (rect.width + rect.height) * 2;
    
    // 底色灰色边框
    final basePaint = Paint()
      ..color = Colors.grey.withOpacity(0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = lineWidth;
    
    // 绘制底色边框
    final path = Path();
    // 使用圆角矩形绘制底色
    final rrect = RRect.fromRectAndCorners(
      rect,
      topLeft: borderRadius.topLeft,
      topRight: borderRadius.topRight,
      bottomLeft: borderRadius.bottomLeft,
      bottomRight: borderRadius.bottomRight,
    );
    path.addRRect(rrect);
    canvas.drawPath(path, basePaint);
    
    // 绘制燃烧线
    if (progress > 0) {
      final burnPaint = Paint()
        ..color = lineColor
        ..style = PaintingStyle.stroke
        ..strokeWidth = lineWidth
        ..strokeCap = StrokeCap.round;
      
      // 创建火线路径
      final burnPath = Path();
      
      // 圆角半径 - 使用borderRadius的最小值作为圆角半径
      final double cornerRadius = Math.min(
        Math.min(borderRadius.topLeft.x, borderRadius.topRight.x),
        Math.min(borderRadius.bottomLeft.x, borderRadius.bottomRight.x)
      );
      
      // 计算当前应该绘制的长度
      final currentLength = perimeter * progress;
      
      // 调整后的矩形，考虑圆角
      final adjustedRect = Rect.fromLTRB(
        rect.left + cornerRadius,
        rect.top + cornerRadius,
        rect.right - cornerRadius,
        rect.bottom - cornerRadius
      );
      
      // 火焰位置
      Offset firePos = Offset.zero;
      
      // 计算各段长度（考虑圆角）
      final double topSide = rect.width - 2 * cornerRadius; // 顶部直线部分长度
      final double rightSide = rect.height - 2 * cornerRadius; // 右侧直线部分长度
      final double bottomSide = rect.width - 2 * cornerRadius; // 底部直线部分长度
      final double leftSide = rect.height - 2 * cornerRadius; // 左侧直线部分长度
      final double cornerArc = cornerRadius * Math.pi / 2; // 每个圆角的弧长
      
      // 计算各个关键点位置
      double remainingLength = currentLength;
      
      if (remainingLength <= topSide) {
        // 1. 仅绘制顶部部分直线
        burnPath.moveTo(adjustedRect.left, rect.top);
        burnPath.lineTo(adjustedRect.left + remainingLength, rect.top);
        firePos = Offset(adjustedRect.left + remainingLength, rect.top);
      } else {
        // 绘制完整顶部
        burnPath.moveTo(adjustedRect.left, rect.top);
        burnPath.lineTo(adjustedRect.right, rect.top);
        remainingLength -= topSide;
        
        if (remainingLength <= cornerArc) {
          // 2. 绘制右上角圆弧
          double sweepAngle = (remainingLength / cornerArc) * Math.pi / 2;
          burnPath.arcTo(
            Rect.fromCircle(center: Offset(adjustedRect.right, adjustedRect.top), radius: cornerRadius),
            -Math.pi / 2, // 起始角度
            sweepAngle, // 扫过角度
            false
          );
          // 计算火焰位置
          double angle = -Math.pi / 2 + sweepAngle;
          firePos = Offset(
            adjustedRect.right + cornerRadius * Math.cos(angle),
            adjustedRect.top + cornerRadius * Math.sin(angle)
          );
        } else {
          // 完整绘制右上角
          burnPath.arcTo(
            Rect.fromCircle(center: Offset(adjustedRect.right, adjustedRect.top), radius: cornerRadius),
            -Math.pi / 2,
            Math.pi / 2,
            false
          );
          remainingLength -= cornerArc;
          
          if (remainingLength <= rightSide) {
            // 3. 绘制右侧部分
            burnPath.lineTo(rect.right, adjustedRect.top + remainingLength);
            firePos = Offset(rect.right, adjustedRect.top + remainingLength);
          } else {
            // 完整绘制右侧
            burnPath.lineTo(rect.right, adjustedRect.bottom);
            remainingLength -= rightSide;
            
            if (remainingLength <= cornerArc) {
              // 4. 绘制右下角圆弧
              double sweepAngle = (remainingLength / cornerArc) * Math.pi / 2;
              burnPath.arcTo(
                Rect.fromCircle(center: Offset(adjustedRect.right, adjustedRect.bottom), radius: cornerRadius),
                0, // 起始角度
                sweepAngle, // 扫过角度
                false
              );
              // 计算火焰位置
              double angle = sweepAngle;
              firePos = Offset(
                adjustedRect.right + cornerRadius * Math.cos(angle),
                adjustedRect.bottom + cornerRadius * Math.sin(angle)
              );
            } else {
              // 完整绘制右下角
              burnPath.arcTo(
                Rect.fromCircle(center: Offset(adjustedRect.right, adjustedRect.bottom), radius: cornerRadius),
                0,
                Math.pi / 2,
                false
              );
              remainingLength -= cornerArc;
              
              if (remainingLength <= bottomSide) {
                // 5. 绘制底部部分
                burnPath.lineTo(adjustedRect.right - remainingLength, rect.bottom);
                firePos = Offset(adjustedRect.right - remainingLength, rect.bottom);
              } else {
                // 完整绘制底部
                burnPath.lineTo(adjustedRect.left, rect.bottom);
                remainingLength -= bottomSide;
                
                if (remainingLength <= cornerArc) {
                  // 6. 绘制左下角圆弧
                  double sweepAngle = (remainingLength / cornerArc) * Math.pi / 2;
                  burnPath.arcTo(
                    Rect.fromCircle(center: Offset(adjustedRect.left, adjustedRect.bottom), radius: cornerRadius),
                    Math.pi / 2, // 起始角度
                    sweepAngle, // 扫过角度
                    false
                  );
                  // 计算火焰位置
                  double angle = Math.pi / 2 + sweepAngle;
                  firePos = Offset(
                    adjustedRect.left + cornerRadius * Math.cos(angle),
                    adjustedRect.bottom + cornerRadius * Math.sin(angle)
                  );
                } else {
                  // 完整绘制左下角
                  burnPath.arcTo(
                    Rect.fromCircle(center: Offset(adjustedRect.left, adjustedRect.bottom), radius: cornerRadius),
                    Math.pi / 2,
                    Math.pi / 2,
                    false
                  );
                  remainingLength -= cornerArc;
                  
                  if (remainingLength <= leftSide) {
                    // 7. 绘制左侧部分
                    burnPath.lineTo(rect.left, adjustedRect.bottom - remainingLength);
                    firePos = Offset(rect.left, adjustedRect.bottom - remainingLength);
                  } else {
                    // 完整绘制左侧
                    burnPath.lineTo(rect.left, adjustedRect.top);
                    remainingLength -= leftSide;
                    
                    if (remainingLength <= cornerArc) {
                      // 8. 绘制左上角圆弧
                      double sweepAngle = (remainingLength / cornerArc) * Math.pi / 2;
                      burnPath.arcTo(
                        Rect.fromCircle(center: Offset(adjustedRect.left, adjustedRect.top), radius: cornerRadius),
                        Math.pi, // 起始角度
                        sweepAngle, // 扫过角度
                        false
                      );
                      // 计算火焰位置
                      double angle = Math.pi + sweepAngle;
                      firePos = Offset(
                        adjustedRect.left + cornerRadius * Math.cos(angle),
                        adjustedRect.top + cornerRadius * Math.sin(angle)
                      );
                    } else {
                      // 绘制完整路径
                      burnPath.arcTo(
                        Rect.fromCircle(center: Offset(adjustedRect.left, adjustedRect.top), radius: cornerRadius),
                        Math.pi,
                        Math.pi / 2,
                        false
                      );
                      firePos = Offset(adjustedRect.left, rect.top);
                    }
                  }
                }
              }
            }
          }
        }
      }
      
      // 绘制烧绳线
      canvas.drawPath(burnPath, burnPaint);
      
      // 在进度结束位置绘制火焰效果
      if (progress < 1.0) {
        // 绘制火焰
        final firePaint = Paint()
          ..color = fireColor
          ..style = PaintingStyle.fill;
        
        // 主火焰
        canvas.drawCircle(firePos, lineWidth * 1.8, firePaint);
        
        // 小火星效果
        final sparkPaint = Paint()
          ..color = fireColor.withOpacity(0.7)
          ..style = PaintingStyle.fill;
        
        // 创建随机效果 - 小火星
        for (int i = 0; i < 3; i++) {
          // 使用伪随机效果，基于进度值和索引
          final seed = (progress * 1000 + i * 10).toInt();
          final offsetX = ((seed % 100) / 100 - 0.5) * lineWidth * 2;
          final offsetY = ((seed % 77) / 77 - 0.5) * lineWidth * 2;
          final sparkSize = (seed % 88) / 88 * lineWidth * 0.8;
          
          canvas.drawCircle(
            Offset(firePos.dx + offsetX, firePos.dy + offsetY),
            sparkSize,
            sparkPaint
          );
        }
      }
    }
  }
  
  @override
  bool shouldRepaint(covariant BurnLinePainter oldDelegate) {
    return oldDelegate.progress != progress ||
           oldDelegate.lineColor != lineColor ||
           oldDelegate.fireColor != fireColor ||
           oldDelegate.lineWidth != lineWidth ||
           oldDelegate.borderRadius != borderRadius;
  }
}
